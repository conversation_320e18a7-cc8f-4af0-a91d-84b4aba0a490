{"name": "oroi-dashboard", "version": "0.1.0", "private": true, "type": "module", "dependencies": {"@ant-design/charts": "^2.1.1", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "antd": "^5.8.6", "axios": "^1.5.0", "dayjs": "^1.11.9", "jodit-react": "^1.3.39", "mapbox-gl": "^3.3.0", "moment": "^2.29.4", "quill-image-uploader": "^1.3.0", "quill-toggle-fullscreen-button": "^0.1.2", "react": "^18.2.0", "react-csv": "^2.2.2", "react-dom": "^18.2.0", "react-filerobot-image-editor": "^4.5.2", "react-gesture-responder": "^2.1.0", "react-grid-dnd": "^2.1.2", "react-h5-audio-player": "^3.9.3", "react-helmet-async": "^2.0.5", "react-icons": "^4.10.1", "react-konva": "^18.2.10", "react-mapbox-gl": "^5.1.1", "react-quill": "^2.0.0", "react-router-dom": "^6.11.2", "styled-components": "^6.1.1", "tailwindcss": "^3.3.5", "web-vitals": "^2.1.4"}, "scripts": {"dev": "vite", "start": "vite", "build": "vite build", "preview": "vite preview", "test": "vitest"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@vitejs/plugin-react": "^4.6.0", "@vitest/ui": "^3.2.4", "autoprefixer": "^10.4.21", "jsdom": "^26.1.0", "vite": "^7.0.0", "vitest": "^3.2.4"}}
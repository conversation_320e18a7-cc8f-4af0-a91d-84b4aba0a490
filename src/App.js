import { ConfigProvider, theme } from "antd";
import React, { useState } from "react";
import { HelmetProvider } from "react-helmet-async";
import { createBrowserRouter, RouterProvider } from "react-router-dom";
import useLocalStorage from "./hooks/use-localStorage";
import mainTheme from "./theme.json";
import { ROLES } from "./util/constant/dataConstants";
import { generateRoutes } from "./util/functions";
import ALL_ROUTES from "./util/Route";

function App() {
  const [profile, SetProfile] = useLocalStorage("profile", {
    role: ROLES.DEFAULT,
  });
  const { defaultAlgorithm, darkAlgorithm } = theme;
  const [isDarkMode, setIsDarkMode] = useState(false);

  if (Object.values(ROLES).findIndex((el) => el === profile.role) === -1) {
    localStorage.clear();
    document.cookie = "SAID=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;";
  }

  const router = createBrowserRouter(
    generateRoutes(
      ALL_ROUTES({
        data: profile,
        profile,
        SetProfile,
        setIsDarkMode,
        isDarkMode,
      }),
      profile?.role
    )
  );
  return (
    <HelmetProvider>
      <ConfigProvider
        theme={{
          ...mainTheme,
          algorithm: isDarkMode ? darkAlgorithm : defaultAlgorithm,
        }}
      >
        <RouterProvider router={router} />
      </ConfigProvider>
    </HelmetProvider>
  );
}

export default App;

import { Button, notification, Row } from "antd";
import React, { useState } from "react";
import useHttp from "../../../hooks/use-http";
import CONSTANTS from "../../../util/constant/CONSTANTS";
import { payloadGenerator } from "../CRUD-Component";
import ModalFormCreator from "../ModalFormCreator";

const BulkFlashcardViaImage = ({ refresher }) => {
  const [open, setOpen] = useState(false);
  const API = useHttp();
  const onCreate = (value, clear) => {
    let payload = payloadGenerator(
      value,
      CONSTANTS.FORM_FIELD.FLASHCCARD_BULK_IMAGE,
      true
    );

    API.sendRequest(
      CONSTANTS.API.Flashcard.bulkImage,
      (res) => {
        // console.log(res?.data, res);
        if (res?.failureCount) {
          notification.info({
            duration: 4,
            message: `Bulk image upload complete \n success : ${res?.successCount} \n  failed : ${res?.failureCount} `,
          });
        } else {
          notification.success({
            duration: 2,
            message: `Bulk image upload complete \n success : ${res?.successCount} ::: All `,
          });
        }
        setOpen(false);
        clear();
        if (refresher) {
          refresher();
        }
      },
      payload
    );
  };
  return (
    <>
      <Row>
        <Button
          onClick={() => {
            setOpen(true);
          }}
        >
          Add New Flashcard
        </Button>
      </Row>
      <ModalFormCreator
        open={open}
        loading={API.isLoading}
        onCancel={() => {
          setOpen(false);
        }}
        onCreate={onCreate}
        formData={{}}
        name="Bulk Upload Flashcard via Images"
        menuFields={CONSTANTS.FORM_FIELD.FLASHCCARD_BULK_IMAGE}
        SubmitName={"Bulk Upload"}
      />
    </>
  );
};

export default BulkFlashcardViaImage;

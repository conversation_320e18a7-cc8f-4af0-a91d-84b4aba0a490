import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, Spin } from "antd";
import React, { useEffect, useState } from "react";
import { FaPlus } from "react-icons/fa";
import useHttp from "../../../hooks/use-http";
import CONSTANTS from "../../../util/constant/CONSTANTS";
import CustomSearchBar from "../Custom-search";

const FlashCardSearchandAdd = ({
  query = {},
  endpointObject = null,
  payloadmodifier = (r) => r,
  refresher = () => {},
  optionmodifier = (option) => {
    return {
      OldData: { ...option },
      id: option?.id,
      AllBeachId: option?.id,
      name: option?.name,
      // description: `${option?.city?.name}, ${option?.city?.state?.name}, ${option?.city?.state?.country?.name}`,
      description: `${option?.city?.name}, ${option?.state?.name}, ${option?.country?.name}`,
    };
  },
  searchQuery = "search"
}) => {
  const [modal, setModal] = useState(false);
  const [search, setSearch] = useState("");
  const [option, setOptions] = useState([]);
  const API = useHttp();
  const InnerAPI = useHttp();
  useEffect(() => {
    if (search.trim().length > 0) {
      API.sendRequest(
        CONSTANTS.API.Flashcard.getAll,
        (res) => {
          setOptions(
            res?.data?.rows?.map(
              (el) => ({ ...optionmodifier(el), OldData: { ...option } } ?? {})
            ) ?? []
          );
        },
        { [searchQuery]: search, page: 1, limit: 15, ...query },
        null,
        () => {
          setOptions(null);
        }
      );
    } else {
      setOptions([]);
    }
  }, [search]);
  return (
    <>
      {" "}
      <Row>
        <Button
          onClick={() => {
            setModal(true);
          }}
        >
          Add New Flashcard
        </Button>
      </Row>
      <Modal
        open={modal}
        onCancel={() => {
          refresher();
          setModal(false);
        }}
        footer={null}
      >
        <Row className=" mt-6 mb-3">
          <CustomSearchBar setKeyword={(v) => setSearch(v)} isSearch />
        </Row>
        {API.isLoading ? (
          <Row className=" items-center justify-center">
            <Spin size="large" />
          </Row>
        ) : search?.trim().length > 0 && option?.length ? (
          <List
            style={{
              height: 400,
              overflow: "auto",
              padding: "0 16px",
              border: "1px solid rgba(140, 140, 140, 0.35)",
            }}
            dataSource={option}
            renderItem={(item) => (
              <List.Item key={item?.id} className="flex">
                <List.Item.Meta
                  // avatar={<Avatar src={item?.picture.large} />}
                  title={item?.name}
                  description={item?.description}
                />
                <Button
                  type="default"
                  shape="circle"
                  className="flex items-center justify-center"
                  loading={InnerAPI.isLoading}
                  onClick={() => {
                    // const payloadB = payloadmodifier(item);
                    // console.log(payloadB)
                    if (
                      endpointObject &&
                      endpointObject?.endpoint &&
                      endpointObject?.type
                    ) {
                      const payload = payloadmodifier(item);
                      InnerAPI.sendRequest(
                        endpointObject,
                        (res) => {},
                        payload,"added successfully"
                      );
                    }
                  }}
                >
                  {!InnerAPI.isLoading && <FaPlus />}
                </Button>
              </List.Item>
            )}
          />
        ) : option !== null || search.trim().length === 0 ? (
          <Alert type="info" message="Please Search the Flashcard" />
        ) : (
          <Alert type="error" message="Error While fetching" />
        )}
      </Modal>
    </>
  );
};

export default FlashCardSearchandAdd;

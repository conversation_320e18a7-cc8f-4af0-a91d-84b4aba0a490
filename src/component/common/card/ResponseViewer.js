import {
  Mo<PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  Typography,
  Divider,
  Card,
  Collapse,
  Tag,
} from "antd";
import React, { useState } from "react";
import {
  CodeOutlined,
  InfoCircleOutlined,
  CloseOutlined,
} from "@ant-design/icons";

const { Title, Text } = Typography;
const { Panel } = Collapse;

const ResponseViewer = ({ data, title, fullDetails }) => {
  const [open, setOpen] = useState(false);

  const renderJsonData = (jsonData) => {
    return (
      <Card
        size="small"
        style={{
          backgroundColor: "#f5f5f5",
          borderRadius: "4px",
          maxHeight: "400px",
          overflow: "auto",
        }}
      >
        <pre style={{ margin: 0, fontSize: "13px" }}>
          {JSON.stringify(jsonData, null, 2)}
        </pre>
      </Card>
    );
  };

  const renderFullDetails = () => {
    return (
      <div>
        <Title level={4} style={{ marginBottom: 16 }}>
          <InfoCircleOutlined /> Full Details
        </Title>
        <Collapse defaultActiveKey={["0"]} ghost>
          {Object.entries(fullDetails).map(([key, value], index) => (
            <Panel
              header={
                <Space>
                  <Text strong>{key}</Text>
                  <Tag color="blue">
                    {typeof value === "object" ? "Object" : typeof value}
                  </Tag>
                </Space>
              }
              key={index}
            >
              {renderJsonData(value)}
            </Panel>
          ))}
        </Collapse>
      </div>
    );
  };

  return (
    <>
      <Modal
        open={open}
        onCancel={() => setOpen(false)}
        footer={null}
        width={800}
        closeIcon={<CloseOutlined />}
      >
        <>
          <div>
            <Title level={5} style={{ marginBottom: 8 }}>
              <CodeOutlined /> Basic Response
            </Title>
            {renderJsonData(data)}
          </div>
          <Divider />
          {renderFullDetails()}
        </>
      </Modal>
      <Button
        type="primary"
        onClick={() => setOpen(true)}
        icon={<CodeOutlined />}
      >
        View Details
      </Button>
    </>
  );
};

export default ResponseViewer;

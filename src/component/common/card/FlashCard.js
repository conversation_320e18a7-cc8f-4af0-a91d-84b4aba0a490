import { DislikeOutlined, LikeOutlined, StarOutlined } from "@ant-design/icons";
import { Badge, Card, Image, Spin, Tooltip, Typography } from "antd";
import React from "react";
import { AudioPlayer } from "../../../util/constant/AudioPlayer";

const { Text, Title } = Typography;

const Flashcard = ({ data, isLoading }) => {
  return (isLoading ?
    <Spin spinning={isLoading} className="flex justify-center items-center" style={{
        display:"flex"
        ,justifyContent:"center",
        alignItems:"center",
        margin:"auto"
    }}
    
    /> :
    <Badge.Ribbon text={` ${data?.language?.name} ( ${data?.language?.code} - ${data?.language?.englishName})`} color="blue">
      <Card
        hoverable
        className={`max-w-sm rounded overflow-hidden shadow-lg`}
        cover={
          !isLoading && (
          
            <Image
              alt={data?.word}
              src={data?.image || "https://via.placeholder.com/300"} // Placeholder if no image
              className="h-48 w-full object-cover aspect-[3/4]"
              />
              
          )
        }
      >
        {!isLoading && (
          <>
            <div className="px-6 py-4">
              {/* <Badge.Ribbon text={data?.language?.name} color="blue"> */}
                <Title level={3} className="text-center">
                  {data?.word}
                </Title>
              {/* </Badge.Ribbon> */}
              <Tooltip title={data?.pronunciation}>
                <Text className="block text-gray-500 text-sm italic">
                  {data?.pronunciation}
                </Text>
              </Tooltip>
              <Text className="block text-gray-700 text-base mt-2">
                {data?.meaning}
              </Text>
              {data?.example && (
                <Text className="block text-gray-600 text-sm mt-2">
                  Example: {data?.example}
                </Text>
              )}
            </div>

            <div className="px-6 pt-4 pb-2">
              <div className="flex items-center justify-between">
                <Tooltip title={data?.audio ? "Play Pronunciation" : "No Pronunciation Found"}>
                  <AudioPlayer src={data?.audio} />
                 {/* {data?.audio ? <AudioOutlined
                    onClick={() => {data?.audio && new Audio(data?.audio ).play()}}
                    className="text-xl cursor-pointer text-blue-500"
                  /> :<AudioOutlined
                  // onClick={() => {data?.audio && new Audio(data?.audio ).play()}}
                  className="text-xl cursor-not-allowed text-gray-500"
                />} */}
                </Tooltip>
                <div className="flex space-x-4">
                  <Tooltip title="Likes">
                    <div className="flex items-center">
                      <LikeOutlined className="mr-1 text-lg text-green-500" />
                      <Text>{data?.likes}</Text>
                    </div>
                  </Tooltip>
                  <Tooltip title="Dislikes">
                    <div className="flex items-center">
                      <DislikeOutlined className="mr-1 text-lg text-red-500" />
                      <Text>{data?.dislikes}</Text>
                    </div>
                  </Tooltip>
                  <Tooltip title="Bookmarks">
                    <div className="flex items-center">
                      <StarOutlined className="mr-1 text-lg text-yellow-500" />
                      <Text>{data?.bookmarks}</Text>
                    </div>
                  </Tooltip>
                </div>
              </div>
            </div>

            <div className="px-6 py-2 mt-3 flex justify-between">
              {data?.coursesCount && <Text>Courses: {data?.coursesCount}</Text>}
             {data?.groupsCount && <Text>Groups: {data?.groupsCount}</Text>}
            </div>
          </>
        )}
      </Card>
      </Badge.Ribbon>
  );
};

export default Flashcard;

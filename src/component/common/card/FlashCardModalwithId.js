import { But<PERSON>, Modal } from "antd";
import React, { useState } from "react";
import useHttp from "../../../hooks/use-http";
import CONSTANTS from "../../../util/constant/CONSTANTS";
import Flashcard from "./FlashCard";

const FlashCardModalwithId = ({ flashcardId , data }) => {
  const API = useHttp();
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [flashcardData, setFlshcardData] = useState({});
  const showModal = () => {
    setIsModalVisible(true);
    if(data?.word && data?.pronunciation && data?.meaning){
        setFlshcardData({...data});
    }else{

        FetchFlashcard(flashcardId);
    }
  };

  const handleCancel = () => {
    setIsModalVisible(false);
  };
  const FetchFlashcard = (id) => {
    const GetOneFlshacrdAPIEnd = {
      ...CONSTANTS.API.Flashcard.getOne,
    };
    GetOneFlshacrdAPIEnd.endpoint = `${GetOneFlshacrdAPIEnd?.endpoint}${id}`;

    API.sendRequest(GetOneFlshacrdAPIEnd, (res) => {
    //   console.log(res?.data);
      setFlshcardData(res?.data);
    });
  };
  return (
    <>
      <Modal
        title="Flashcard Details"
        open={isModalVisible}
        onCancel={handleCancel}
        footer={null}
        styles={{
          body: {
            backgroundColor: "transparent",
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
          },
        }}
        centered
        width={450} // Customize width as needed
      >
        <Flashcard data={flashcardData} isLoading={API.isLoading} />
      </Modal>
      <Button type="primary" onClick={showModal}>
        View Flashcard
      </Button>
    </>
  );
};

export default FlashCardModalwithId;

import { Col, Image, Menu, Row } from "antd";
import Sider from "antd/es/layout/Sider";
import { useNavigate } from "react-router-dom";
import logo from "../../../asset/logos/icon.png";
import { ROLES } from "../../../util/constant/dataConstants";
import { generateMenuFromRoutes } from "../../../util/functions";
import ALL_ROUTES from "../../../util/Route";

const SidebarMenu = ({ profile }) => {
  const navigate = useNavigate();

  return (
    <Sider
      theme="light"
      collapsible
      breakpoint="md"
      collapsedWidth="50"
      className=" top-16 !static"
    >
      <Row className="items-center justify-center">
        <Col span={24} className="flex items-center justify-center py-3">
          <Image preview={false} src={logo} width={40} />
        </Col>
      </Row>
      <Menu
        defaultSelectedKeys={[window.location.pathname]}
        selectedKeys={[window.location.pathname]}
        mode="inline"
        items={generateMenuFromRoutes(
          ALL_ROUTES(),
          profile?.role || ROLES.ADMIN
        )}
        onClick={(e) => {
          if (e.keyPath.length) {
            navigate(e.key);
          }
        }}
      />
    </Sider>
  );
};

export default SidebarMenu;

import { Layout } from "antd";
import { Content } from "antd/es/layout/layout";
import { useEffect, useState } from "react";
import { Outlet, useNavigate } from "react-router-dom";
import useHttp from "../../../hooks/use-http";
import { deleteAuthDetails, getAuthToken } from "../../../util/API/authStorage";
import CONSTANTS from "../../../util/constant/CONSTANTS";
import { loginRoot } from "../../../util/Route";
import HeaderBar from "./HeaderBar";
import ProfileDrawer from "./ProfileDrawer";
import SidebarMenu from "./SidebarMenu";

const AppLayout = (props) => {
  const { profile, SetProfile, setIsDarkMode, isDarkMode } = props;
  const navigate = useNavigate();
  const [visible, setVisible] = useState(false);
  const [UserData, setUserData] = useState({});
  const api = useHttp();

  useEffect(() => {
    if (!(getAuthToken() !== undefined && getAuthToken() !== null)) {
      navigate(loginRoot);
      return;
    }
    if (!CONSTANTS.GETMe) {
      api.sendRequest(CONSTANTS.API.getMe, (res) => {
        CONSTANTS.GETMe = res?.data;
        setUserData(res?.data);
        SetProfile({ ...res?.data });
      });
    }
  }, []);

  const handleLogout = () => {
    deleteAuthDetails();
    localStorage.clear();
    navigate("/");
  };

  return (
    <Layout hasSider={true} style={{ height: "100vh" }}>
      <SidebarMenu profile={profile} />
      <Layout className="site-layout">
        <HeaderBar
          UserData={UserData}
          setVisible={setVisible}
          isDarkMode={isDarkMode}
          setIsDarkMode={setIsDarkMode}
        />
        <Content style={{ maxHeight: "100%", overflowY: "auto" }}>
          <div
            style={{
              height: "100%",
              margin: "0 16px",
            }}
          >
            <Outlet />
          </div>
        </Content>
      </Layout>
      <ProfileDrawer
        visible={visible}
        onClose={() => setVisible(false)}
        UserData={UserData}
        handleLogout={handleLogout}
      />
    </Layout>
  );
};

export default AppLayout;

import { <PERSON><PERSON>, But<PERSON>, Drawer } from "antd";
import Profile from "../../../asset/image/dummy-avatar.jpg";

const ProfileDrawer = ({ visible, onClose, UserData, handleLogout }) => {
  return (
    <Drawer
      placement="right"
      closable={true}
      onClose={onClose}
      open={visible}
      // width="100%"
      // bodyStyle={{ padding: "20px" }}

    >
      <div className="flex flex-col items-center text-center profile-drawer">
        <Avatar
          size={100}
          style={{ color: "#ffffff", backgroundColor: "#000000" }}
          src={UserData?.profilePic || Profile}
          className="mt-10"
        />
        <div className="mt-5 text-2xl font-medium">
          {UserData?.username || "Website Admin"}
        </div>
        <div className="my-1 text-slate-400">
          Role: {UserData?.role || "User"}
        </div>
        <div className="text-slate-500">
          {UserData?.email || "<EMAIL>"}
        </div>
        <Button
          danger
          htmlType="submit"
          className="mt-5 w-full max-w-xs h-10"
          onClick={handleLogout}
        >
          Logout
        </Button>
      </div>
      <hr className="my-5" style={{ background: "#E4E8F0" }} />
    </Drawer>
  );
};

export default ProfileDrawer;

import { Avatar, Col, Row, Switch } from "antd";
import { Header } from "antd/es/layout/layout";
import Profile from "../../../asset/image/dummy-avatar.jpg";

const HeaderBar = ({ UserData, setVisible, isDarkMode, setIsDarkMode }) => {
  return (
    <Header
      // style={{
      //   padding: 0,
      //   backgroundColor: "#FFFFFF",
      //   display: "flex",
      //   justifyContent: "space-between",
      //   alignItems: "center",
      // }}
      className={`w-full sticky top-0 ${
        !isDarkMode ? "bg-white" : "bg-[#141414]"
      }`}
    >
      <Row className="items-center justify-center py-3">
        {/* <Col span={6} className="flex items-center justify-start">
          <Image preview={false} src={logo} width={40} />
        </Col> */}

        {/* <Row
        align="middle"
        justify="end"
        style={{ width: "100%", padding: "0 16px" }}
      > */}
        <Col span={4} className="center flex">
          <Switch
            checked={isDarkMode}
            onChange={() => {
              setIsDarkMode((prev) => !prev);
            }}
          />
        </Col>
        <Col span={20} className="flex items-center justify-end">
          <Avatar
            src={UserData?.profilePic || Profile}
            size={40}
            style={{
              border: "1px solid black",
              cursor: "pointer",
            }}
            onClick={() => setVisible(true)}
          />
        </Col>
      </Row>
    </Header>
  );
};

export default HeaderBar;

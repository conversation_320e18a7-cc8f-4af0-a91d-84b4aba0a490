import {
  CheckCircleTwoTone,
  CloseCircleTwoTone,
  DeleteOutlined,
  DownloadOutlined,
  EditOutlined,
  EyeOutlined
} from "@ant-design/icons";
import {
  Button,
  Image,
  Input,
  InputNumber,
  Popconfirm,
  Switch
} from "antd";
import "react-h5-audio-player/lib/styles.css";

import moment from "moment/moment";
import { BsQuestionCircle } from "react-icons/bs";
import { FaEye } from "react-icons/fa";

export const alphanumericSort = (a, b, value) => {
  const valA = a?.[value];
  const valB = b?.[value];

  // Handle null or undefined values first
  if (valA == null && valB == null) return 0;
  if (valA == null) return 1;
  if (valB == null) return -1;

  // Handle numbers
  const numA = parseFloat(valA);
  const numB = parseFloat(valB);
  if (!isNaN(numA) && !isNaN(numB)) {
    return numA - numB;
  }

  // Handle dates
  const dateA = new Date(valA);
  const dateB = new Date(valB);
  if (!isNaN(dateA.getTime()) && !isNaN(dateB.getTime())) {
    return dateA - dateB;
  }

  // Handle arrays (sort by their length)
  if (Array.isArray(valA) && Array.isArray(valB)) {
    return valA.length - valB.length;
  }

  // Handle objects (convert them to strings for comparison)
  if (typeof valA === 'object' && typeof valB === 'object') {
    return JSON.stringify(valA).localeCompare(JSON.stringify(valB));
  }

  // Handle strings
  if (typeof valA === 'string' && typeof valB === 'string') {
    return valA.localeCompare(valB);
  }

  // Fallback for other types (default comparison)
  return String(valA).localeCompare(String(valB));
};

export const dateSort = (a, b, value) => {
  return moment(a?.[value]).subtract(moment(b?.[value]));
};

export function convertToPST(dateString, formatearray) {
  // Parse the date using moment
  const date = moment(dateString);

  // Manually apply the PST offset of UTC-8
  const pstDate = date.utcOffset(-8);

  // Return the formatted date string in PST
  if (formatearray) {
    return [pstDate.format("YYYY-MM-DD"), pstDate.format("hh:mm A")];
  }
  return pstDate.format("YYYY-MM-DD hh:mm A [PST]");
}
export const RenderActionButtons = (value) => {
  let ButtonsArray = [];
  if (value?.Delete) {
    ButtonsArray.push(RenderDeleteButton(value.Delete));
  }
  if (value?.Edit) {
    ButtonsArray.push(RenderEditButton(value.Edit));
  }
  if (value?.View) {
    ButtonsArray.push(RenderViewButton(value.View));
  }
  if (value?.Download) {
    ButtonsArray.push(RenderDownloadButton(value.Download));
  }
  return (
    <div
      style={{
        display: "flex",
        justifyContent: "space-around",
        // paddingRight: "5px",
      }}
    >
      {ButtonsArray?.map((el, i) => (
        <div key={i}>{el}</div>
      ))}
    </div>
  );
};

// Delete Button
export const RenderDeleteButton = (value = {}) => {
  const {
    id,
    onClick,
    message = "Are you sure you want to delete this?",
  } = value;
  return (
    <Popconfirm
      title={
        <p className=" flex items-center justify-center">
          <BsQuestionCircle className=" text-red-600 mx-2" /> {message}
        </p>
      }
      onConfirm={() => onClick(id)}
      align="center"
      icon={null}
    >
      <Button type="primary" danger>
        {value?.name ? value?.name : <DeleteOutlined />}
      </Button>
      {/* <Button type="primary">{value?.name}</Button> */}
    </Popconfirm>
  );
};

// Delete button without confimation model
const RenderDeleteWithoutConfirmation = (value) => {
  return (
    <Popconfirm
      title="Are you sure you want to delete this beach?"
      open={false}
      onOpenChange={() => value?.onClick(value.key)}
      onConfirm={() => value?.onClick(value.key)}
    >
      {value?.name ? (
        value?.name
      ) : (
        <DeleteOutlined style={{ color: "black" }} />
      )}
    </Popconfirm>
  );
};
// Download Button
export const RenderDownloadButton = (value) => {
  return (
    <DownloadOutlined
      onClick={() => {
        window.open(value, "Download");
      }}
    />
  );
};

// Edit Button
export const RenderEditButton = (value = {}) => {
  const { id = null, onClick = () => {} } = value;
  if (!id) {
    return <></>;
  }
  return (
    <Button
      type="primary"
      onClick={() => {
        onClick(id);
      }}
    >
      <EditOutlined />
    </Button>
  );
};

// View Image Button
export const RenderViewImageButton = (value) => {
  return (
    <Image
      width={20}
      src={<FaEye />}
      className="pointer"
      preview={{
        mask: false,
        src: value,
      }}
    />
  );
};

// Render Accept and Reject Button
export const RenderAcceptRejectButton = (value) => {
  const { id, onAccept, onReject } = value;

  return (
    <div>
      <Popconfirm
        placement="top"
        title={"Are you sure to Accept this request?"}
        // description={description}
        onConfirm={() => {
          onAccept(id);
        }}
        okText="Yes"
        cancelText="No"
      >
        <Button style={{ backgroundColor: "#52c41a" }}>
          <CheckCircleTwoTone twoToneColor="#52c41a" />
        </Button>
      </Popconfirm>
      <Popconfirm
        placement="top"
        title={"Are you sure to Reject this request?"}
        // description={description}
        onConfirm={() => {
          onReject(id);
        }}
        okText="Yes"
        cancelText="No"
      >
        <Button className="ml-2 bg-red-600">
          <CloseCircleTwoTone twoToneColor="#dc2626" />
        </Button>
      </Popconfirm>
    </div>
  );
};

// Render View Button
export const RenderViewButton = (value = {}) => {
  const { id, onClick, link } = value;
  return !!!link ? (
    <Button
      onClick={() => {
        if (onClick) {
          onClick(id);
        }
      }}
      type="primary"
    >
      <EyeOutlined
      // onClick={() => {
      //   // onClick(id);
      // }}
      />
    </Button>
  ) : (
    <Button href={link} type="primary">
      <EyeOutlined
      // onClick={() => {
      //   // onClick(id);
      // }}
      />
    </Button>
  );
};

// Render Images
export const ImageRanders = (value, extraClass) => {
  let imageSrc = value;
  if (!imageSrc) {
    imageSrc =
      "https://upload.wikimedia.org/wikipedia/commons/1/14/No_Image_Available.jpg?20200913095930";
    // logo;
  }
  return (
    <div>
      <Image
        width={50}
        height={50}
        className={`rounded-full object-cover ${extraClass}`}
        src={imageSrc}
        alt={value}
      />
    </div>
  );
};

// export const CustomAudioPlayer = ({ src, extraClass }) => {
//   // = "https://audiodemos.github.io/vctk_set0/ground_truth.wav"
//   return (
//     <Tooltip title={src ? "Play Pronunciation" : "No Pronunciation Found"}>
//       {src ? (
//         <AudioOutlined
//           onClick={() => {
//             src && new Audio(src).play();
//           }}
//           className="text-xl cursor-pointer text-blue-500"
//         />
//       ) : (
//         <AudioOutlined
//           // onClick={() => {data?.audio && new Audio(data?.audio ).play()}}
//           className="text-xl cursor-not-allowed text-gray-500"
//         />
//       )}
//     </Tooltip>
//   );
  // return !!src ? (
  //   <div className={`custom-audio-player ${extraClass} border-none`}>
  //     <AudioPlayer
  //       autoPlay={false}
  //       src={src}
  //       onPlay={(e) => console.log("Playing")}
  //       onPause={(e) => console.log("Paused")}
  //       customAdditionalControls={[]} // Remove unwanted controls
  //       customVolumeControls={[]} // Customize or remove volume controls
  //       showJumpControls={false} // Remove jump controls
  //       layout="horizontal-reverse" // Optionally reverse the layout
  //       className=""
  //     />
  //   </div>
  // ) : (
  //   "No audio Found"
  // );
// };

// Toggle Button
export const RenderToggleButton = (value) => {
  const { checked, id, onClick } = value;
  return (
    <div>
      <Switch
        onChange={() => {
          onClick(id, checked);
        }}
        checked={checked}
      />
    </div>
  );
};
export const RenderInputField = (Upvalue) => {
  const { value, id, onChange } = Upvalue;
  return (
    <div>
      <InputNumber
        // id="rankInput"
        // onChange={() => {
        //   onClick(id, checked);
        // }}
        controls={false}
        value={value}
        // value={value}
        onBlur={(e) => {
          // console.log("Blur Event happended ", parseInt(e.target.value), id);
          onChange({ value: parseInt(e.target.value), id });
          // } else {
          //   notification.error({ message: "Enter valid Integer Value" });
          // }
        }}
        // onPointerOut={(e) => {
        //   console.log(e, "Pointer Out happended ");
        // }}
        // checked={checked}
      />
    </div>
  );
};

// Checkbox render
export const RenderCheckbox = (value) => {
  const { checked, id, onClick } = value;
  return (
    <div>
      <Input
        type="checkbox"
        checked={!checked}
        onChange={() => {
          onClick(id, checked);
        }}
      />
    </div>
  );
};

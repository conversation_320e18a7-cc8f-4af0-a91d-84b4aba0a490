import FlashCardModalwithId from "../../component/common/card/FlashCardModalwithId";
import <PERSON>Viewer from "../../component/common/card/ResponseViewer";
import { AudioPlayer } from "./AudioPlayer";
import {
  ACTIVE_INACTIVE_ARRAY,
  OPTION_ARRAY,
  QUESTION_TYPES_ARRAY,
  ROLES,
  ROLES_ARRAY,
} from "./dataConstants";
import {
  alphanumericSort,
  ImageRanders,
  RenderDeleteButton,
  RenderEditButton,
  RenderToggleButton,
  RenderViewButton,
} from "./rendermethods";

const CONSTANTS = {
  GETMe: null,
  TABLE: {
    GROUP_FLASHCARD: [
      {
        title: "S. No",
        dataIndex: "rowNo",
        // sorter: (a, b) => alphanumericSort(a, b, "no"),
        width: 120,
      },
      {
        title: "Image",
        dataIndex: "image",
        width: 80,
        render: (v) => ImageRanders(v, "rounded-sm"),
      },
      // {
      //   title: "View",
      //   dataIndex: "view",
      //   render: RenderViewButton,
      //   // sorter: (a, b) => alphanumericSort(a, b, "no"),
      //   width: 80,
      // },
      {
        title: "Word",
        dataIndex: "word",
        width: 160,
        sorter: (a, b) => alphanumericSort(a, b, "word"),
      },
      {
        title: "Pronunciation",
        dataIndex: "pronunciation",
        width: 160,
        sorter: (a, b) => alphanumericSort(a, b, "pronunciation"),
      },
      {
        title: "Audio",
        dataIndex: "audio",
        width: 380,
        render: (v) => <AudioPlayer src={v} />,
      },
      {
        title: "Meaning",
        dataIndex: "meaning",
        width: 160,
        sorter: (a, b) => alphanumericSort(a, b, "meaning"),
      },
      // {
      //   title: "Language",
      //   dataIndex: "language",
      //   width: 180,
      //   render: (el) => {
      //     return ` ${el?.name} ( ${el?.code} - ${el?.englishName})`;
      //   },
      //   // sorter: (a, b) => alphanumericSort(a, b, "meaning"),
      // },
      // {
      //   title : "FlashView",
      //   dataIndex :"id",
      //   render : (v,row)=> {
      //   console.log(row)
      //   return <FlashCardModalwithId flashcardId={v} data={row} />},
      //   width: 180,

      // },

      {
        title: "Example",
        dataIndex: "example",
        width: 160,
        sorter: (a, b) => alphanumericSort(a, b, "example"),
      },

      {
        title: "Action",
        dataIndex: "deleteTableRow",
        // render: (v) =>
        //   RenderDeleteButton({
        //     ...v,
        //     message: "Are you Sure to delete this Category ?",
        //   }),
        render: RenderDeleteButton,
        width: 80,
        // role: [ROLES.SUPER_ADMIN],
      },
    ],
    CATEGORY: [
      {
        title: "S. No",
        dataIndex: "rowNo",
        // sorter: (a, b) => alphanumericSort(a, b, "no"),
        width: 120,
      },
      {
        title: "Name",
        dataIndex: "name",
        width: 160,

        // render: ImageRanders,
        sorter: (a, b) => alphanumericSort(a, b, "name"),
      },
      {
        title: "Action",
        dataIndex: "editTableRow",
        width: 80,
        render: RenderEditButton,
      },
      {
        title: "Action",
        dataIndex: "deleteTableRow",
        // render: (v) =>
        //   RenderDeleteButton({
        //     ...v,
        //     message: "Are you Sure to delete this Category ?",
        //   }),
        render: RenderDeleteButton,
        width: 80,
        role: [ROLES.SUPER_ADMIN],
      },
    ],
    QUESTION: [
      {
        title: "S. No",
        dataIndex: "rowNo",
        width: 120,
        // sorter: (a, b) => alphanumericSort(a, b, "rowNo"),
      },
      {
        title: "View",
        dataIndex: "view",
        render: RenderViewButton,
        // sorter: (a, b) => alphanumericSort(a, b, "no"),
        width: 80,
      },
      {
        title: "Question",
        dataIndex: "question",
        width: 200,
        sorter: (a, b) => alphanumericSort(a, b, "question"),
      },
      {
        title: "Question Type",
        dataIndex: "questionType",
        width: 150,
        sorter: (a, b) => alphanumericSort(a, b, "questionType"),
      },
      {
        title: "Explanation",
        dataIndex: "explanation",
        width: 250,
        // Optionally, you can use a custom renderer if needed
        // render: ExplanationRenderer,
      },
      {
        title: "Active",
        dataIndex: "status",
        width: 140,
        render: RenderToggleButton,
      },
      {
        title: "Edit",
        dataIndex: "editTableRow",
        width: 80,
        render: RenderEditButton,
      },
      {
        title: "Delete",
        dataIndex: "deleteTableRow",
        width: 80,
        render: RenderDeleteButton,
        // Uncomment and modify if you need a confirmation message
        // render: (v) => RenderDeleteButton({
        //   ...v,
        //   message: "Are you sure you want to delete this question?",
        // }),
        role: [ROLES.SUPER_ADMIN],
      },
    ],
    OPTION: [
      {
        title: "S. No",
        dataIndex: "rowNo",
        width: 120,
        // sorter: (a, b) => alphanumericSort(a, b, "rowNo"),
      },
      {
        title: "Option Text",
        dataIndex: "optionText",
        width: 200,
        sorter: (a, b) => alphanumericSort(a, b, "optionText"),
      },
      {
        title: "Correct",
        dataIndex: "isCorrect",
        width: 100,
        render: (value) => (value ? "Yes" : "No"),
        // sorter: (a, b) => a.isCorrect - b.isCorrect,
      },
      {
        title: "Edit",
        dataIndex: "editTableRow",
        width: 80,
        render: RenderEditButton,
      },
      {
        title: "Delete",
        dataIndex: "deleteTableRow",
        width: 80,
        render: RenderDeleteButton,
        // Uncomment and modify if you need a confirmation message
        // render: (v) => RenderDeleteButton({
        //   ...v,
        //   message: "Are you sure you want to delete this option?",
        // }),
        role: [ROLES.SUPER_ADMIN],
      },
    ],
    LANGUAGE: [
      {
        title: "S. No",
        dataIndex: "rowNo",
        // sorter: (a, b) => alphanumericSort(a, b, "no"),
        width: 120,
      },
      {
        title: "Name",
        dataIndex: "name",
        width: 160,
        sorter: (a, b) => alphanumericSort(a, b, "name"),
      },
      {
        title: "Code",
        dataIndex: "code",
        width: 160,
        sorter: (a, b) => alphanumericSort(a, b, "code"),
      },
      {
        title: "Flag",
        dataIndex: "flag",
        width: 160,
        sorter: (a, b) => alphanumericSort(a, b, "flag"),
      },
      {
        title: "English Name",
        dataIndex: "englishName",
        width: 160,
        sorter: (a, b) => alphanumericSort(a, b, "englishName"),
      },
      {
        title: "Active",
        dataIndex: "status",
        width: 140,
        render: RenderToggleButton,
        // sorter: (a, b) => alphanumericSort(a, b, "englishName"),
        filters: ACTIVE_INACTIVE_ARRAY,
        filterMultiple: false,
        onFilter: (value, record) => true,
        filterSearch: true,
      },
      {
        title: "Premium",
        dataIndex: "premium",
        width: 140,
        render: RenderToggleButton,
        filters: ACTIVE_INACTIVE_ARRAY,
        filterMultiple: false,
        onFilter: (value, record) => true,
        filterSearch: true,
      },
      {
        title: "Pronunciation",
        dataIndex: "pronunciation",
        width: 160,
        sorter: (a, b) => alphanumericSort(a, b, "pronunciation"),
      },
      {
        title: "Action",
        dataIndex: "editTableRow",
        width: 80,
        render: RenderEditButton,
      },
      {
        title: "Action",
        dataIndex: "deleteTableRow",
        // render: (v) =>
        //   RenderDeleteButton({
        //     ...v,
        //     message: "Are you Sure to delete this Category ?",
        //   }),
        render: RenderDeleteButton,
        width: 80,
        role: [ROLES.SUPER_ADMIN],
      },
    ],
    GROUP: [
      {
        title: "S. No",
        dataIndex: "rowNo",
        // sorter: (a, b) => alphanumericSort(a, b, "no"),
        width: 120,
      },
      {
        title: "View",
        dataIndex: "view",
        render: RenderViewButton,
        // sorter: (a, b) => alphanumericSort(a, b, "no"),
        width: 80,
      },
      {
        title: "Image",
        dataIndex: "image",
        width: 80,
        render: (v) => ImageRanders(v, "rounded-sm"),
      },
      {
        title: "Name",
        dataIndex: "name",
        width: 160,
        sorter: (a, b) => alphanumericSort(a, b, "name"),
      },
      {
        title: "Description",
        dataIndex: "description",
        width: 160,
        sorter: (a, b) => alphanumericSort(a, b, "description"),
      },
      {
        title: "Language",
        dataIndex: "language",
        width: 180,
        render: (el) => {
          return ` ${el?.name} ( ${el?.code} - ${el?.englishName})`;
        },
        sorter: (a, b) => alphanumericSort(a, b, "language"),
        // sorter: (a, b) => alphanumericSort(a, b, "meaning"),
      },
      {
        title: "flashcard counts",
        dataIndex: "flashcardcounts",
        width: 160,
        sorter: (a, b) => alphanumericSort(a, b, "flashcardcounts"),
      },
      {
        title: "Active",
        dataIndex: "status",
        width: 140,
        render: RenderToggleButton,
      },
      {
        title: "Premium",
        dataIndex: "premium",
        width: 140,
        render: RenderToggleButton,
      },
      {
        title: "Action",
        dataIndex: "editTableRow",
        width: 80,
        render: RenderEditButton,
      },
      {
        title: "Action",
        dataIndex: "deleteTableRow",
        // render: (v) =>
        //   RenderDeleteButton({
        //     ...v,
        //     message: "Are you Sure to delete this Category ?",
        //   }),
        render: RenderDeleteButton,
        width: 80,
        role: [ROLES.SUPER_ADMIN],
      },
    ],
    COURSE: [
      {
        title: "S. No",
        dataIndex: "rowNo",
        // sorter: (a, b) => alphanumericSort(a, b, "no"),
        width: 120,
      },
      {
        title: "Image",
        dataIndex: "image",
        width: 80,
        render: (v) => ImageRanders(v, "rounded-sm"),
      },
      {
        title: "View",
        dataIndex: "view",
        render: RenderViewButton,
        // sorter: (a, b) => alphanumericSort(a, b, "no"),
        width: 80,
      },
      {
        title: "Name",
        dataIndex: "name",
        width: 160,
        sorter: (a, b) => alphanumericSort(a, b, "name"),
      },
      {
        title: "Description",
        dataIndex: "description",
        width: 160,
        sorter: (a, b) => alphanumericSort(a, b, "description"),
      },
      {
        title: "flashcard counts",
        dataIndex: "flashcardcounts",
        width: 160,
        sorter: (a, b) => alphanumericSort(a, b, "flashcardcounts"),
      },
      {
        title: "Language",
        dataIndex: "language",
        width: 180,
        render: (el) => {
          return ` ${el?.name} ( ${el?.code} - ${el?.englishName})`;
        },
        // sorter: (a, b) => alphanumericSort(a, b, "language"),
      },
      {
        title: "Level",
        dataIndex: "level",
        width: 180,
        render: (el) => {
          return `${el?.name}`;
        },
        // sorter: (a, b) => alphanumericSort(a, b, "meaning"),
      },
      {
        title: "Active",
        dataIndex: "status",
        width: 140,
        render: RenderToggleButton,
      },
      {
        title: "Premium",
        dataIndex: "premium",
        width: 140,
        render: RenderToggleButton,
      },
      {
        title: "Action",
        dataIndex: "editTableRow",
        width: 80,
        render: RenderEditButton,
      },
      {
        title: "Action",
        dataIndex: "deleteTableRow",
        // render: (v) =>
        //   RenderDeleteButton({
        //     ...v,
        //     message: "Are you Sure to delete this Category ?",
        //   }),
        render: RenderDeleteButton,
        width: 80,
        role: [ROLES.SUPER_ADMIN],
      },
    ],
    LEVEL: [
      {
        title: "S. No",
        dataIndex: "rowNo",
        // sorter: (a, b) => alphanumericSort(a, b, "no"),
        width: 120,
      },
      {
        title: "Name",
        dataIndex: "name",
        width: 160,
        sorter: (a, b) => alphanumericSort(a, b, "name"),
      },
      {
        title: "Description",
        dataIndex: "description",
        width: 160,
        sorter: (a, b) => alphanumericSort(a, b, "description"),
      },
      {
        title: "Action",
        dataIndex: "editTableRow",
        width: 80,
        render: RenderEditButton,
      },
      {
        title: "Action",
        dataIndex: "deleteTableRow",
        // render: (v) =>
        //   RenderDeleteButton({
        //     ...v,
        //     message: "Are you Sure to delete this Category ?",
        //   }),
        render: RenderDeleteButton,
        width: 80,
        role: [ROLES.SUPER_ADMIN],
      },
    ],
    LOGS: [
      {
        title: "S. No",
        dataIndex: "rowNo",
        width: 80,
      },
      {
        title: "Timestamp",
        dataIndex: "timestamp",
        sorter: (a, b) => new Date(a.timestamp) - new Date(b.timestamp),
        width: 180,
      },
      {
        title: "Method",
        dataIndex: "method",
        width: 100,
        sorter: (a, b) => alphanumericSort(a, b, "method"),
      },
      {
        title: "Path",
        dataIndex: "path",
        width: 200,
        sorter: (a, b) => alphanumericSort(a, b, "path"),
      },
      {
        title: "User ID",
        dataIndex: "userId",
        width: 160,
        sorter: (a, b) => alphanumericSort(a, b, "userId"),
      },
      {
        title: "Email",
        dataIndex: "userEmail",
        width: 200,
        sorter: (a, b) => alphanumericSort(a, b, "userEmail"),
      },
      {
        title: "Status Code",
        dataIndex: "statusCode",
        width: 120,
        sorter: (a, b) => a.statusCode - b.statusCode,
      },
      {
        title: "IP Address",
        dataIndex: "ipAddress",
        width: 160,
      },
      // {
      //   title: "Payload",
      //   dataIndex: "payload",
      //   width: 160,
      //   render: (v, row) => {
      //     return <ResponseViewer data={v} title="Payload" fullDetails={row} />;
      //   },
      // },
      {
        title: "Full Details",
        dataIndex: "response",
        width: 160,
        render: (v, row) => {
          return <ResponseViewer data={v} fullDetails={row} />;
        },
      },
      {
        title: "User Agent",
        dataIndex: "userAgent",
        width: 200,
      },
      {
        title: "Duration (ms)",
        dataIndex: "duration",
        width: 120,
        sorter: (a, b) => a.duration - b.duration,
      },
    ],
    QUIZ: [
      {
        title: "S. No",
        dataIndex: "rowNo",
        // sorter: (a, b) => alphanumericSort(a, b, "no"),
        width: 120,
      },
      {
        title: "View",
        dataIndex: "view",
        render: RenderViewButton,
        // sorter: (a, b) => alphanumericSort(a, b, "no"),
        width: 80,
      },
      {
        title: "Name",
        dataIndex: "name",
        width: 160,
        sorter: (a, b) => alphanumericSort(a, b, "name"),
      },
      {
        title: "Description",
        dataIndex: "description",
        width: 160,
        sorter: (a, b) => alphanumericSort(a, b, "description"),
      },
      {
        title: "Action",
        dataIndex: "editTableRow",
        width: 80,
        render: RenderEditButton,
      },
      {
        title: "Action",
        dataIndex: "deleteTableRow",
        // render: (v) =>
        //   RenderDeleteButton({
        //     ...v,
        //     message: "Are you Sure to delete this Category ?",
        //   }),
        render: RenderDeleteButton,
        width: 80,
        role: [ROLES.SUPER_ADMIN],
      },
    ],
    REPORT: [
      {
        title: "S. No",
        dataIndex: "rowNo",
        // sorter: (a, b) => alphanumericSort(a, b, "no"),
        width: 120,
      },
      {
        title: "Title",
        dataIndex: "title",
        width: 160,
        sorter: (a, b) => alphanumericSort(a, b, "title"),
      },
      {
        title: "Reason",
        dataIndex: "reason",
        width: 160,
        sorter: (a, b) => alphanumericSort(a, b, "reason"),
      },
      {
        title: "Status",
        dataIndex: "status",
        width: 160,
        sorter: (a, b) => alphanumericSort(a, b, "status"),
      },
      // {
      //   title: "Action",
      //   dataIndex: "editTableRow",
      //   width: 80,
      //   render: RenderEditButton,
      // },
      {
        title: "Action",
        dataIndex: "deleteTableRow",
        // render: (v) =>
        //   RenderDeleteButton({
        //     ...v,
        //     message: "Are you Sure to delete this Category ?",
        //   }),
        render: RenderDeleteButton,
        width: 80,
        role: [ROLES.SUPER_ADMIN],
      },
    ],
    SUPPORT: [
      {
        title: "S. No",
        dataIndex: "rowNo",
        // sorter: (a, b) => alphanumericSort(a, b, "no"),
        width: 120,
      },
      {
        title: "Name",
        dataIndex: "name",
        width: 160,
        sorter: (a, b) => alphanumericSort(a, b, "name"),
      },
      {
        title: "Email",
        dataIndex: "email",
        width: 160,
        sorter: (a, b) => alphanumericSort(a, b, "email"),
      },
      {
        title: "Subject",
        dataIndex: "subject",
        width: 160,
        sorter: (a, b) => alphanumericSort(a, b, "subject"),
      },
      {
        title: "Priority",
        dataIndex: "priority",
        width: 160,
        sorter: (a, b) => alphanumericSort(a, b, "priority"),
      },
      {
        title: "Status",
        dataIndex: "status",
        width: 160,
        sorter: (a, b) => alphanumericSort(a, b, "status"),
      },
      // {
      //   title: "Action",
      //   dataIndex: "editTableRow",
      //   width: 80,
      //   render: RenderEditButton,
      // },
      {
        title: "Action",
        dataIndex: "deleteTableRow",
        // render: (v) =>
        //   RenderDeleteButton({
        //     ...v,
        //     message: "Are you Sure to delete this Category ?",
        //   }),
        render: RenderDeleteButton,
        width: 80,
        role: [ROLES.SUPER_ADMIN],
      },
    ],
    FLASHCARD: [
      {
        title: "S. No",
        dataIndex: "rowNo",
        // sorter: (a, b) => alphanumericSort(a, b, "no"),
        width: 120,
      },
      {
        title: "Image",
        dataIndex: "image",
        width: 80,
        render: (v) => ImageRanders(v, "rounded-sm"),
      },
      {
        title: "View",
        dataIndex: "view",
        render: RenderViewButton,
        // sorter: (a, b) => alphanumericSort(a, b, "no"),
        width: 80,
      },
      {
        title: "Word",
        dataIndex: "word",
        width: 160,
        sorter: (a, b) => alphanumericSort(a, b, "word"),
      },
      {
        title: "Pronunciation",
        dataIndex: "pronunciation",
        width: 160,
        sorter: (a, b) => alphanumericSort(a, b, "pronunciation"),
      },
      {
        title: "Audio",
        dataIndex: "audio",
        width: 100,
        render: (v) => <AudioPlayer src={v} />,
      },
      {
        title: "Meaning",
        dataIndex: "meaning",
        width: 160,
        sorter: (a, b) => alphanumericSort(a, b, "meaning"),
      },
      {
        title: "like",
        dataIndex: "like",
        render: RenderViewButton,
        // sorter: (a, b) => alphanumericSort(a, b, "no"),
        width: 80,
      },
      {
        title: "bookmark",
        dataIndex: "bookmark",
        render: RenderViewButton,
        // sorter: (a, b) => alphanumericSort(a, b, "no"),
        width: 80,
      },
      {
        title: "Language",
        dataIndex: "language",
        width: 180,
        render: (el) => {
          return ` ${el?.name} ( ${el?.code} - ${el?.englishName})`;
        },
        filters: [],
        filterMultiple: false,
        onFilter: (value, record) => true,
        filterSearch: true,
        // sorter: (a, b) => alphanumericSort(a, b, "meaning"),
      },
      {
        title: "FlashView",
        dataIndex: "id",
        render: (v, row) => {
          // console.log(row)
          return <FlashCardModalwithId flashcardId={v} data={row} />;
        },
        width: 180,
      },
      // {
      //   title: "isActive",
      //   dataIndex: "isActive",
      //   width: 160,
      //   sorter: (a, b) => alphanumericSort(a, b, "isActive"),
      // },
      {
        title: "Active",
        dataIndex: "status",
        width: 140,
        render: RenderToggleButton,
      },
      {
        title: "Example",
        dataIndex: "example",
        width: 160,
        sorter: (a, b) => alphanumericSort(a, b, "example"),
      },
      {
        title: "Likes",
        dataIndex: "likes",
        width: 160,
        // sorter: (a, b) => alphanumericSort(a, b, "likes"),
      },
      {
        title: "DisLikes",
        dataIndex: "dislikes",
        width: 160,
        // sorter: (a, b) => alphanumericSort(a, b, "likes"),
      },
      {
        title: "Bookmarks",
        dataIndex: "bookmarks",
        width: 160,
        // sorter: (a, b) => alphanumericSort(a, b, "likes"),
      },
      {
        title: "course counts",
        dataIndex: "coursesCount",
        width: 160,
      },
      {
        title: "group counts",
        dataIndex: "groupsCount",
        width: 160,
      },
      {
        title: "Action",
        dataIndex: "editTableRow",
        width: 80,
        render: RenderEditButton,
      },
      {
        title: "Action",
        dataIndex: "deleteTableRow",
        // render: (v) =>
        //   RenderDeleteButton({
        //     ...v,
        //     message: "Are you Sure to delete this Category ?",
        //   }),
        render: RenderDeleteButton,
        width: 80,
        role: [ROLES.SUPER_ADMIN],
      },
    ],
    SIMILAR_WORDS: [
      {
        title: "S. No",
        dataIndex: "rowNo",
        // sorter: (a, b) => alphanumericSort(a, b, "no"),
        width: 120,
      },

      {
        title: "Word",
        dataIndex: "word",
        width: 160,
        sorter: (a, b) => alphanumericSort(a, b, "word"),
      },
      {
        title: "Pronunciation",
        dataIndex: "pronunciation",
        width: 160,
        sorter: (a, b) => alphanumericSort(a, b, "pronunciation"),
      },
      {
        title: "Active",
        dataIndex: "status",
        width: 140,
        render: RenderToggleButton,
      },
      {
        title: "Action",
        dataIndex: "editTableRow",
        width: 80,
        render: RenderEditButton,
      },
      {
        title: "Action",
        dataIndex: "deleteTableRow",
        // render: (v) =>
        //   RenderDeleteButton({
        //     ...v,
        //     message: "Are you Sure to delete this Category ?",
        //   }),
        render: RenderDeleteButton,
        width: 80,
        // role: [ROLES.SUPER_ADMIN],
      },
    ],
    PROPOSECARD: [
      {
        title: "S. No",
        dataIndex: "rowNo",
        // sorter: (a, b) => alphanumericSort(a, b, "no"),
        width: 120,
      },
      {
        title: "Image",
        dataIndex: "image",
        width: 80,
        render: (v) => ImageRanders(v, "rounded-sm"),
      },
      {
        title: "Word",
        dataIndex: "word",
        width: 160,
        sorter: (a, b) => alphanumericSort(a, b, "word"),
      },
      {
        title: "Pronunciation",
        dataIndex: "pronunciation",
        width: 160,
        sorter: (a, b) => alphanumericSort(a, b, "pronunciation"),
      },
      {
        title: "Audio",
        dataIndex: "audio",
        width: 380,
        render: (v) => <AudioPlayer src={v} />,
      },
      {
        title: "Meaning",
        dataIndex: "meaning",
        width: 160,
        sorter: (a, b) => alphanumericSort(a, b, "meaning"),
      },
      {
        title: "Language",
        dataIndex: "language",
        width: 180,
        render: (el) => {
          return ` ${el?.name} ( ${el?.code} - ${el?.englishName})`;
        },
        // sorter: (a, b) => alphanumericSort(a, b, "meaning"),
      },
      // {
      //   title: "isActive",
      //   dataIndex: "isActive",
      //   width: 160,
      //   sorter: (a, b) => alphanumericSort(a, b, "isActive"),
      // },
      // {
      //   title: "Active",
      //   dataIndex: "status",
      //   width: 140,
      //   render: RenderToggleButton,
      // },
      {
        title: "Example",
        dataIndex: "example",
        width: 160,
        sorter: (a, b) => alphanumericSort(a, b, "example"),
      },
      // {
      //   title: "Likes",
      //   dataIndex: "likes",
      //   width: 160,
      //   // sorter: (a, b) => alphanumericSort(a, b, "likes"),
      // },
      // {
      //   title: "DisLikes",
      //   dataIndex: "dislikes",
      //   width: 160,
      //   // sorter: (a, b) => alphanumericSort(a, b, "likes"),
      // },
      // {
      //   title: "Bookmarks",
      //   dataIndex: "bookmarks",
      //   width: 160,
      //   // sorter: (a, b) => alphanumericSort(a, b, "likes"),
      // },

      // {
      //   title: "Action",
      //   dataIndex: "editTableRow",
      //   width: 80,
      //   render: RenderEditButton,
      // },
      {
        title: "Action",
        dataIndex: "deleteTableRow",
        // render: (v) =>
        //   RenderDeleteButton({
        //     ...v,
        //     message: "Are you Sure to delete this Category ?",
        //   }),
        render: RenderDeleteButton,
        width: 80,
        role: [ROLES.SUPER_ADMIN],
      },
    ],

    TAG: [
      {
        title: "S. No",
        dataIndex: "no",
        sorter: (a, b) => alphanumericSort(a, b, "no"),
        width: 120,
      },
      {
        title: "Name",
        dataIndex: "tag",
        width: 160,

        // render: ImageRanders,
        sorter: (a, b) => alphanumericSort(a, b, "tag"),
      },
      {
        title: "Action",
        dataIndex: "editTableRow",
        width: 80,
        render: RenderEditButton,
      },
      {
        title: "Action",
        dataIndex: "deleteTableRow",
        // render: (v) =>
        //   RenderDeleteButton({
        //     ...v,
        //     message: "Are you Sure to delete this Category ?",
        //   }),
        render: RenderDeleteButton,
        width: 80,
        role: [ROLES.SUPER_ADMIN],
      },
    ],
    USERS: [
      {
        title: "S. No",
        dataIndex: "rowNo",
        // sorter: (a, b) => alphanumericSort(a, b, "rowNo"),
        width: 120,
      },
      // {
      //   title: "View",
      //   dataIndex: "view",
      //   render: RenderViewButton,
      //   // sorter: (a, b) => alphanumericSort(a, b, "no"),
      //   width: 80,
      // },
      {
        title: "Name",
        dataIndex: "username",
        width: 160,
        sorter: (a, b) => alphanumericSort(a, b, "username"),
      },
      {
        title: "Email",
        dataIndex: "email",
        width: 160,
        sorter: (a, b) => alphanumericSort(a, b, "email"),
      },
      {
        title: "Active",
        dataIndex: "status",
        width: 140,
        render: RenderToggleButton,
      },
      {
        title: "Role",
        dataIndex: "role",
        width: 140,
        filters: ROLES_ARRAY,
        filterMultiple: false,
        onFilter: (value, record) => true,
        filterSearch: true,
        // render: (v) => {
        //   return `${v}`.toUpperCase();
        // },
      },

      // {
      //   title: "Join Date",
      //   dataIndex: "createdAt",
      //   width: 180,
      //   render: (v) => {
      //     const value = convertToPST(v, true);
      //     return (
      //       <>
      //         <p>{value[0]}</p>
      //         <p>{`${value[1]} [PST]`} </p>
      //       </>
      //     );
      //   },
      //   sorter: (a, b) => moment(a.createdAt).subtract(moment(b.createdAt)),
      //   // sorter: (a, b) => alphanumericSort(a, b, "updatedAt"),
      // },
      // {
      //   title: "Action",
      //   dataIndex: "editTableRow",
      //   render: RenderEditButton,
      //   width: 80,
      //   // role: [ROLES.SUPER_ADMIN],
      // },
      {
        title: "Action",
        dataIndex: "deleteTableRow",
        render: RenderDeleteButton,
        width: 80,
        // role: [ROLES.SUPER_ADMIN],
      },
      // {
      //   title: "Action",
      //   dataIndex: "editTableRow",
      //   render: RenderEditButton,
      // },
      // {
      //   title: "Action",
      //   dataIndex: "deleteTableRow",
      //   render: RenderDeleteButton,
      //   width: 80,
      // },
      // { title: "Action", dataIndex: "delete", render: RenderDeleteButton },
    ],
  },
  FORM_FIELD: {
    LOGIN_PAGE_MODAL: [
      {
        no: 1,
        // Label: "User Name",
        name: "name",
        id: "name",
        type: "text",
        placeholder: "User name",
        required: true,
        width: 24,
      },
      {
        no: 1,
        // Label: "Password",
        name: "password",
        width: 24,
        id: "password",
        type: "password",
        placeholder: "Password",
        required: true,
      },
    ],
    CATEGORY: [
      {
        no: 1,
        Label: "Category",
        name: "name",
        id: "name",
        type: "text",
        placeholder: "Enter a Category",
        required: true,
        width: 24,
      },
    ],
    FLASHCCARD_BULK_IMAGE: [
      {
        no: 1,
        Label: "Language",
        name: "languageId",
        id: "languageId",
        type: "select",
        placeholder: "Select a Language",
        required: true,
        width: 24,
      },
      {
        no: 2,
        Label: "Flash Images",
        name: "image",
        id: "image",
        type: "file",
        placeholder: "Enter a Flash Images",
        maxCount: 50,
        acceptFormat: "image/*",
        required: true,
        multiple: true,
        width: 24,
      },
    ],
    LEVEL: [
      {
        no: 1,
        Label: "Name",
        name: "name",
        id: "name",
        type: "text",
        placeholder: "Enter a Name",
        required: true,
        width: 24,
      },
      {
        no: 2,
        Label: "Description",
        name: "description",
        id: "description",
        type: "text",
        placeholder: "Enter a Description",
        required: true,
        width: 24,
      },
    ],
    GROUP: [
      {
        no: 1,
        Label: "Name",
        name: "name",
        id: "name",
        type: "text",
        placeholder: "Enter a Name",
        required: true,
        width: 24,
      },
      {
        no: 2,
        Label: "Description",
        name: "description",
        id: "description",
        type: "text",
        placeholder: "Enter a Description",
        required: true,
        width: 24,
      },

      {
        no: 4,
        Label: "Language",
        name: "languageId",
        id: "languageId",
        type: "select",
        placeholder: "Select a Language",
        required: true,
        width: 8,
      },
      {
        no: 3,
        Label: "Image",
        name: "image",
        id: "image",
        type: "file",
        placeholder: "Enter a Image",
        // required: true,
        width: 8,
      },
    ],
    COURSE: [
      {
        no: 1,
        Label: "Name",
        name: "name",
        id: "name",
        type: "text",
        placeholder: "Enter a Name",
        required: true,
        width: 24,
      },
      {
        no: 2,
        Label: "Description",
        name: "description",
        id: "description",
        type: "text",
        placeholder: "Enter a Description",
        required: true,
        width: 24,
      },

      {
        no: 4,
        Label: "Language",
        name: "languageId",
        id: "languageId",
        type: "select",
        placeholder: "Select a Language",
        required: true,
        width: 8,
      },
      {
        no: 5,
        Label: "Level",
        name: "levelId",
        id: "levelId",
        type: "select",
        placeholder: "Select a Level",
        required: true,
        width: 8,
      },
      {
        no: 3,
        Label: "Image",
        name: "image",
        id: "image",
        type: "file",
        placeholder: "Enter a Image",
        // required: true,
        width: 8,
      },
    ],
    LANGUAGE: [
      {
        no: 1,
        Label: "Name",
        name: "name",
        id: "name",
        type: "text",
        placeholder: "Enter a Name",
        required: true,
        width: 12,
      },
      {
        no: 2,
        Label: "Code",
        name: "code",
        id: "code",
        type: "text",
        placeholder: "Enter a Code",
        required: true,
        width: 12,
      },
      {
        no: 3,
        Label: "Flag",
        name: "flag",
        id: "flag",
        type: "text",
        placeholder: "Enter a Flag",
        required: true,
        width: 12,
      },
      {
        no: 4,
        Label: "English Name",
        name: "englishName",
        id: "englishName",
        type: "text",
        placeholder: "Enter a English Name",
        required: true,
        width: 12,
      },
      {
        no: 5,
        Label: "Pronunciation",
        name: "pronunciation",
        id: "pronunciation",
        type: "text",
        placeholder: "Enter a Pronunciation",
        required: true,
        width: 12,
      },
    ],
    FLASHCARD: [
      {
        no: 1,
        Label: "Word",
        name: "word",
        id: "word",
        type: "text",
        placeholder: "Enter a Word",
        required: true,
        width: 12,
      },
      {
        no: 2,
        Label: "Pronunciation",
        name: "pronunciation",
        id: "pronunciation",
        type: "text",
        placeholder: "Enter a Pronunciation",
        required: true,
        width: 12,
      },
      {
        no: 3,
        Label: "Meaning",
        name: "meaning",
        id: "meaning",
        type: "text",
        placeholder: "Enter a Meaning",
        required: true,
        width: 24,
      },
      {
        no: 4,
        Label: "Example",
        name: "example",
        id: "example",
        type: "text",
        placeholder: "Enter a Example",
        required: true,
        width: 24,
      },
      {
        no: 4,
        Label: "Language",
        name: "languageId",
        id: "languageId",
        type: "select",
        placeholder: "Enter a Language",
        required: true,
        width: 8,
      },
      {
        no: 4,
        Label: "Image",
        name: "image",
        id: "image",
        type: "file",
        placeholder: "Enter a Image",
        // required: true,
        width: 8,
      },
      {
        no: 4,
        Label: "Audio",
        name: "audio",
        id: "audio",
        type: "file",
        placeholder: "Enter a Audio",
        // required: true,
        width: 8,
      },
    ],
    SIMILAR_WORDS: [
      {
        no: 1,
        Label: "Word",
        name: "word",
        id: "word",
        type: "text",
        placeholder: "Enter a Word",
        required: true,
        width: 12,
      },
    ],
    TAG: [
      {
        no: 1,
        Label: "Tag",
        name: "tag",
        id: "tag",
        type: "text",
        placeholder: "Enter a Tag",
        required: true,
        width: 24,
      },
    ],
    QUIZ: [
      {
        no: 1,
        Label: "Name",
        name: "name",
        id: "name",
        type: "text",
        placeholder: "Enter a Name",
        required: true,
        width: 24,
      },
      {
        no: 2,
        Label: "Description",
        name: "description",
        id: "description",
        type: "text",
        placeholder: "Enter a Description",
        required: true,
        width: 24,
      },
      {
        no: 3,
        Label: "Questions",
        name: "questions",
        id: "questions",
        type: "multifield",
        initialValue: [{}],
        required: true,
        removeName: "Remove Question",
        addName: "Add Question",
        AllFields: [
          {
            no: 1,
            Label: "Question",
            name: "question",
            id: "question",
            type: "text",
            placeholder: "Enter Question",
            required: true,
          },
          {
            no: 2,
            Label: "Question Type",
            name: "questionType",
            id: "questionType",
            type: "select",
            option: QUESTION_TYPES_ARRAY,
            placeholder: "Select Question Type",
            required: true,
          },
          {
            no: 3,
            Label: "Explanation",
            name: "explanation",
            id: "explanation",
            type: "textarea",
            placeholder: "Enter Explanation",
            required: false,
          },
          {
            no: 4,
            Label: "Options",
            name: "options",
            id: "options",
            type: "multifield",
            initialValue: [{}],
            required: true,
            removeName: "Remove Option",
            addName: "Add Option",
            AllFields: [
              {
                no: 1,
                Label: "Option Text",
                name: "optionText",
                id: "optionText",
                type: "text",
                placeholder: "Enter Option Text",
                required: true,
                width: 20,
              },
              {
                no: 2,
                Label: "Correct",
                name: "isCorrect",
                id: "isCorrect",
                type: "select",
                option: OPTION_ARRAY,
                placeholder: "Select Correct",
                required: true,
                width: 4,
              },
            ],
          },
        ],
      },
    ],

    QUESTION: [
      {
        no: 1,
        Label: "Question",
        name: "question",
        id: "question",
        type: "text",
        placeholder: "Enter Question",
        required: true,
      },
      {
        no: 2,
        Label: "Question Type",
        name: "questionType",
        id: "questionType",
        type: "select",
        option: QUESTION_TYPES_ARRAY,
        placeholder: "Select Question Type",
        required: true,
      },
      {
        no: 3,
        Label: "Explanation",
        name: "explanation",
        id: "explanation",
        type: "textarea",
        placeholder: "Enter Explanation",
        required: false,
      },
      {
        no: 4,
        Label: "Options",
        name: "options",
        id: "options",
        type: "multifield",
        initialValue: [{}],
        required: true,
        removeName: "Remove Option",
        addName: "Add Option",
        AllFields: [
          {
            no: 1,
            Label: "Option Text",
            name: "optionText",
            id: "optionText",
            type: "text",
            placeholder: "Enter Option Text",
            required: true,
            width: 20,
          },
          {
            no: 2,
            Label: "Correct",
            name: "isCorrect",
            id: "isCorrect",
            type: "select",
            option: OPTION_ARRAY,
            placeholder: "Select Correct",
            required: true,
            width: 4,
          },
        ],
      },
    ],
    EDIT_QUIZ: [
      {
        no: 1,
        Label: "Name",
        name: "name",
        id: "name",
        type: "text",
        placeholder: "Enter a Name",
        required: true,
        width: 24,
      },
      {
        no: 2,
        Label: "Description",
        name: "description",
        id: "description",
        type: "text",
        placeholder: "Enter a Description",
        required: true,
        width: 24,
      },
    ],
    EDIT_QUESTION: [
      {
        no: 1,
        Label: "Question",
        name: "question",
        id: "question",
        type: "text",
        placeholder: "Enter Question",
        required: true,
      },
      {
        no: 2,
        Label: "Question Type",
        name: "questionType",
        id: "questionType",
        type: "select",
        option: QUESTION_TYPES_ARRAY,
        placeholder: "Select Question Type",
        required: true,
      },
      {
        no: 3,
        Label: "Explanation",
        name: "explanation",
        id: "explanation",
        type: "textarea",
        placeholder: "Enter Explanation",
        required: false,
      },
    ],
    OPTION: [
      {
        no: 1,
        Label: "Option Text",
        name: "optionText",
        id: "optionText",
        type: "text",
        placeholder: "Enter Option Text",
        required: true,
      },
      {
        no: 2,
        Label: "Correct",
        name: "isCorrect",
        id: "isCorrect",
        type: "select",
        option: OPTION_ARRAY,
        placeholder: "Select Correct",
        required: true,
      },
    ],

    USERS_MODAL: [
      {
        no: 1,
        Label: "Name",
        name: "username",
        id: "username",
        type: "text",
        placeholder: "Enter Name",
        required: true,
      },
      {
        no: 2,
        Label: "Email",
        name: "email",
        id: "email",
        type: "email",
        placeholder: "Enter Email",
        required: true,
      },
      {
        no: 3,
        Label: "Role",
        name: "role",
        id: "role",
        type: "select",
        option: ROLES_ARRAY,
        placeholder: "Select Role",
        required: true,
      },
      // {
      //   no: 2,
      //   Label: "Password",
      //   name: "password",
      //   id: "password",
      //   type: "password",
      //   placeholder: "Enter Password",
      //   required: true,
      // },
    ],
    BLOG_MODAL: [
      {
        no: 1,
        Label: "Title",
        name: "title",
        id: "title",
        type: "text",
        placeholder: "Title",
        required: true,
        autoFocus: true,
      },
      {
        no: 2,
        Label: "Description",
        name: "description",
        id: "description",
        type: "textarea",
        placeholder: "Enter Description",
        required: true,
      },
      {
        no: 10,
        Label: "Meta Title",
        name: "metaTitle",
        id: "metaTitle",
        type: "text",
        placeholder: "Meta Title",
        required: true,
        autoFocus: true,
      },
      {
        no: 11,
        Label: "Meta Description",
        name: "metaDescription",
        id: "metaDescription",
        type: "textarea",
        placeholder: "Enter Meta Description",
        required: true,
      },
      {
        no: 12,
        Label: "Slug",
        name: "slug",
        id: "slug",
        type: "text",
        placeholder: "Slug",
        required: true,
        autoFocus: true,
      },
      {
        no: 3,
        Label: "Overview",
        name: "overview",
        id: "overview",
        // type: "richTextarea",
        type: "SimpleRichTextArea",
        placeholder: "Enter Overview",
        required: true,
      },
      {
        no: 4,
        Label: "Read Time",
        name: "readTime",
        id: "readTime",
        type: "number",
        placeholder: "Enter Read Time",
        required: true,
      },
      {
        no: 5,
        Label: "Category",
        name: "categories",
        id: "categories",
        type: "select",
        placeholder: "Select Category",
        required: true,
        option: [],
        mode: "multiple",
      },
      {
        no: 6,
        Label: "Tags",
        name: "tags",
        id: "tags",
        type: "select",
        placeholder: "Select Tag",
        required: true,
        option: [],
        mode: "multiple",
      },
      {
        no: 6,
        Label: "Image",
        name: "image",
        id: "image",
        type: "file",
        placeholder: " ",
        required: true,
        acceptFormat: "image/*",
      },
      // {
      //   no: 7,
      //   Label: "Alt",
      //   name: "alt",
      //   id: "alt",
      //   type: "text",
      //   placeholder: "Enter Alt",
      //   required: true,
      // },
      // {
      //   no: 8,
      //   Label: "Schedule Release",
      //   name: "release",
      //   id: "release",
      //   type: "date",
      //   placeholder: "Select Date",
      //   showTime: "HH:mm:ss",
      //   format: "YYYY-MM-DD HH:mm:ss",
      //   // required: true,
      // },
    ],
    EDIT_BLOG_MODAL: [
      {
        no: 0,
        // Label: "Image",
        // name: "image",
        id: "imagePreview",
        type: "ImageCenterPreview",
        // placeholder: " ",
        // required: false,
        image: "image",
        // acceptFormat: "image/*",
      },
      {
        no: 1,
        Label: "Title",
        name: "title",
        id: "title",
        type: "text",
        placeholder: "Title",
        required: true,
        autoFocus: true,
      },
      {
        no: 2,
        Label: "Description",
        name: "description",
        id: "description",
        type: "textarea",
        placeholder: "Enter Description",
        required: true,
      },
      {
        no: 10,
        Label: "Meta Title",
        name: "metaTitle",
        id: "metaTitle",
        type: "text",
        placeholder: "Meta Title",
        required: true,
        autoFocus: true,
      },
      {
        no: 11,
        Label: "Meta Description",
        name: "metaDescription",
        id: "metaDescription",
        type: "textarea",
        placeholder: "Enter Meta Description",
        required: true,
      },
      {
        no: 12,
        Label: "Slug",
        name: "slug",
        id: "slug",
        type: "text",
        placeholder: "Slug",
        required: true,
        autoFocus: true,
      },
      {
        no: 3,
        Label: "Overview",
        name: "overview",
        id: "overview",
        // type: "richTextarea",
        type: "SimpleRichTextArea",
        placeholder: "Enter Overview",
        required: true,
      },
      {
        no: 4,
        Label: "Read Time",
        name: "readTime",
        id: "readTime",
        type: "number",
        placeholder: "Enter Read Time",
        required: true,
      },
      {
        no: 5,
        Label: "Category",
        name: "categories",
        id: "categories",
        type: "select",
        placeholder: "Select Category",
        required: true,
        option: [],
        mode: "multiple",
      },
      {
        no: 6,
        Label: "Tags",
        name: "tags",
        id: "tags",
        type: "select",
        placeholder: "Select Tag",
        required: true,
        option: [],
        mode: "multiple",
      },
      {
        no: 6,
        Label: "Image",
        name: "image",
        id: "image",
        type: "fileWithPreview",
        placeholder: " ",
        required: false,
        image: "image",
        acceptFormat: "image/*",
      },
      // {
      //   no: 7,
      //   Label: "Alt",
      //   name: "alt",
      //   id: "alt",
      //   type: "text",
      //   placeholder: "Enter Alt",
      // },
      // {
      //   no: 9,
      //   Label: "Schedule Release",
      //   name: "release",
      //   id: "release",
      //   type: "date",
      //   placeholder: "Select Date",
      //   showTime:'HH:mm:ss',
      //   format:"YYYY-MM-DD HH:mm:ss",
      //   required: true,
      //   disabled:true,
      // },
    ],
  },

  API: {
    login: {
      type: "POST",
      endpoint: "/admin/user/login",
    },
    signUp: { type: "POST", endpoint: "/admin/user/signup" },
    getMe: { type: "GET", endpoint: "/user/me" },
    overview: { type: "GET", endpoint: "/admin/overview" },
    Analytics: {
      InternAnalytics: {
        type: "GET",
        endpoint: "/admin/general/interns-analytics",
      },
    },
    Dashboard: {
      analytics: {
        type: "GET",
        endpoint: "/admin/dashboard/analytics",
      },
      graph: {
        type: "GET",
        endpoint: "/admin/dashboard/getDailyData",
      },
      languageWise: {
        type: "GET",
        endpoint: "/admin/dashboard/langugespecific",
      },
      Flashcards: {
        type: "GET",
        endpoint: "/admin/dashboard/toplikedislike",
      },
    },
    User: {
      getAll: {
        type: "GET",
        endpoint: "/admin/user",
      },
      create: {
        type: "POST",
        endpoint: "/admin/user",
      },
      update: {
        type: "PATCH",
        endpoint: "/admin/user/",
      },
      delete: {
        type: "DELETE",
        endpoint: "/admin/user/",
      },
      getOne: {
        type: "GET",
        endpoint: "/admin/user/:id",
      },
    },
    Category: {
      getAll: {
        type: "GET",
        endpoint: "/admin/category",
      },
      create: {
        type: "POST",
        endpoint: "/admin/category/",
      },
      update: {
        type: "PATCH",
        endpoint: "/admin/category/",
      },
      delete: {
        type: "DELETE",
        endpoint: "/admin/category/",
      },
      getOne: {
        type: "GET",
        endpoint: "/admin/category/",
      },
    },
    Flashcard: {
      getAll: {
        type: "GET",
        endpoint: "/admin/flashcard",
      },
      create: {
        type: "POST",
        endpoint: "/admin/flashcard/",
      },
      bulkImage: {
        type: "POST",
        endpoint: "/admin/flashcard/bulk-image",
      },
      update: {
        type: "PATCH",
        endpoint: "/admin/flashcard/",
      },
      likedislike: {
        type: "GET",
        endpoint: "/admin/flashcard/likedislike/",
      },
      bookmark: {
        type: "GET",
        endpoint: "/admin/flashcard/bookmark/",
      },
      delete: {
        type: "DELETE",
        endpoint: "/admin/flashcard/",
      },
      getOne: {
        type: "GET",
        endpoint: "/admin/flashcard/",
      },
    },
    SimilarWords: {
      getAll: {
        type: "GET",
        endpoint: "/admin/similarWord",
      },
      create: {
        type: "POST",
        endpoint: "/admin/similarWord/",
      },
      update: {
        type: "PATCH",
        endpoint: "/admin/similarWord/",
      },
      delete: {
        type: "DELETE",
        endpoint: "/admin/similarWord/",
      },
      getOne: {
        type: "GET",
        endpoint: "/admin/similarWord/",
      },
    },
    GroupFlashcard: {
      getAll: {
        type: "GET",
        endpoint: "/admin/groupflashcard",
      },
      create: {
        type: "POST",
        endpoint: "/admin/groupflashcard/",
      },
      update: {
        type: "PATCH",
        endpoint: "/admin/groupflashcard/",
      },
      delete: {
        type: "DELETE",
        endpoint: "/admin/groupflashcard/",
      },
      getOne: {
        type: "GET",
        endpoint: "/admin/groupflashcard/",
      },
    },
    CourseFlashcard: {
      getAll: {
        type: "GET",
        endpoint: "/admin/courseflashcard",
      },
      create: {
        type: "POST",
        endpoint: "/admin/courseflashcard/",
      },
      update: {
        type: "PATCH",
        endpoint: "/admin/courseflashcard/",
      },
      delete: {
        type: "DELETE",
        endpoint: "/admin/courseflashcard/",
      },
      getOne: {
        type: "GET",
        endpoint: "/admin/courseflashcard/",
      },
    },
    Proposecard: {
      getAll: {
        type: "GET",
        endpoint: "/admin/proposecard",
      },
      create: {
        type: "POST",
        endpoint: "/admin/proposecard/",
      },
      update: {
        type: "PATCH",
        endpoint: "/admin/proposecard/",
      },
      delete: {
        type: "DELETE",
        endpoint: "/admin/proposecard/",
      },
      getOne: {
        type: "GET",
        endpoint: "/admin/proposecard/",
      },
    },
    Language: {
      getAll: {
        type: "GET",
        endpoint: "/admin/language",
      },
      create: {
        type: "POST",
        endpoint: "/admin/language/",
      },
      update: {
        type: "PATCH",
        endpoint: "/admin/language/",
      },
      delete: {
        type: "DELETE",
        endpoint: "/admin/language/",
      },
      getOne: {
        type: "GET",
        endpoint: "/admin/language/",
      },
    },
    Level: {
      getAll: {
        type: "GET",
        endpoint: "/admin/level",
      },
      create: {
        type: "POST",
        endpoint: "/admin/level/",
      },
      update: {
        type: "PATCH",
        endpoint: "/admin/level/",
      },
      delete: {
        type: "DELETE",
        endpoint: "/admin/level/",
      },
      getOne: {
        type: "GET",
        endpoint: "/admin/level/",
      },
    },
    Logs: {
      getAll: {
        type: "GET",
        endpoint: "/admin/api-logs",
      },
      create: {
        type: "POST",
        endpoint: "/admin/api-logs/",
      },
      update: {
        type: "PATCH",
        endpoint: "/admin/api-logs/",
      },
      delete: {
        type: "DELETE",
        endpoint: "/admin/api-logs/",
      },
      getOne: {
        type: "GET",
        endpoint: "/admin/api-logs/",
      },
      getAnalytics: {
        type: "GET",
        endpoint: "/admin/api-logs/analytics",
      },
    },
    Support: {
      getAll: {
        type: "GET",
        endpoint: "/admin/support",
      },
      create: {
        type: "POST",
        endpoint: "/admin/support/",
      },
      update: {
        type: "PATCH",
        endpoint: "/admin/support/",
      },
      delete: {
        type: "DELETE",
        endpoint: "/admin/support/",
      },
      getOne: {
        type: "GET",
        endpoint: "/admin/support/",
      },
    },
    Quiz: {
      getAll: {
        type: "GET",
        endpoint: "/admin/quiz",
      },
      create: {
        type: "POST",
        endpoint: "/admin/quiz/",
      },
      update: {
        type: "PATCH",
        endpoint: "/admin/quiz/",
      },
      delete: {
        type: "DELETE",
        endpoint: "/admin/quiz/",
      },
      getOne: {
        type: "GET",
        endpoint: "/admin/quiz/",
      },
    },
    Question: {
      getAll: {
        type: "GET",
        endpoint: "/admin/question",
      },
      create: {
        type: "POST",
        endpoint: "/admin/question/",
      },
      update: {
        type: "PATCH",
        endpoint: "/admin/question/",
      },
      delete: {
        type: "DELETE",
        endpoint: "/admin/question/",
      },
      getOne: {
        type: "GET",
        endpoint: "/admin/question/",
      },
    },
    QuestionOption: {
      getAll: {
        type: "GET",
        endpoint: "/admin/questionoption",
      },
      create: {
        type: "POST",
        endpoint: "/admin/questionoption/",
      },
      update: {
        type: "PATCH",
        endpoint: "/admin/questionoption/",
      },
      delete: {
        type: "DELETE",
        endpoint: "/admin/questionoption/",
      },
      getOne: {
        type: "GET",
        endpoint: "/admin/questionoption/",
      },
    },
    FlashcardReport: {
      getAll: {
        type: "GET",
        endpoint: "/admin/flashcardreport",
      },
      create: {
        type: "POST",
        endpoint: "/admin/flashcardreport/",
      },
      update: {
        type: "PATCH",
        endpoint: "/admin/flashcardreport/",
      },
      delete: {
        type: "DELETE",
        endpoint: "/admin/flashcardreport/",
      },
      getOne: {
        type: "GET",
        endpoint: "/admin/flashcardreport/",
      },
    },
    GroupReport: {
      getAll: {
        type: "GET",
        endpoint: "/admin/groupreport",
      },
      create: {
        type: "POST",
        endpoint: "/admin/groupreport/",
      },
      update: {
        type: "PATCH",
        endpoint: "/admin/groupreport/",
      },
      delete: {
        type: "DELETE",
        endpoint: "/admin/groupreport/",
      },
      getOne: {
        type: "GET",
        endpoint: "/admin/groupreport/",
      },
    },
    CourseReport: {
      getAll: {
        type: "GET",
        endpoint: "/admin/coursereport",
      },
      create: {
        type: "POST",
        endpoint: "/admin/coursereport/",
      },
      update: {
        type: "PATCH",
        endpoint: "/admin/coursereport/",
      },
      delete: {
        type: "DELETE",
        endpoint: "/admin/coursereport/",
      },
      getOne: {
        type: "GET",
        endpoint: "/admin/coursereport/",
      },
    },
    Group: {
      getAll: {
        type: "GET",
        endpoint: "/admin/group",
      },
      create: {
        type: "POST",
        endpoint: "/admin/group/",
      },
      update: {
        type: "PATCH",
        endpoint: "/admin/group/",
      },
      delete: {
        type: "DELETE",
        endpoint: "/admin/group/",
      },
      getOne: {
        type: "GET",
        endpoint: "/admin/group/",
      },
    },
    Course: {
      getAll: {
        type: "GET",
        endpoint: "/admin/course",
      },
      create: {
        type: "POST",
        endpoint: "/admin/course/",
      },
      update: {
        type: "PATCH",
        endpoint: "/admin/course/",
      },
      delete: {
        type: "DELETE",
        endpoint: "/admin/course/",
      },
      getOne: {
        type: "GET",
        endpoint: "/admin/course/",
      },
    },
    Tag: {
      getAll: {
        type: "GET",
        endpoint: "/admin/tag",
      },
      create: {
        type: "POST",
        endpoint: "/admin/tag/",
      },
      update: {
        type: "PATCH",
        endpoint: "/admin/tag/",
      },
      delete: {
        type: "DELETE",
        endpoint: "/admin/tag/",
      },
      getOne: {
        type: "GET",
        endpoint: "/admin/tag/",
      },
    },

    Log: {
      getAll: {
        type: "GET",
        endpoint: "/admin/beachLog",
      },
      create: {
        type: "POST",
        endpoint: "/admin/beachLog/",
      },
      update: {
        type: "PATCH",
        endpoint: "/admin/beachLog/",
      },
      delete: {
        type: "DELETE",
        endpoint: "/admin/beachLog/",
      },
      getOne: {
        type: "GET",
        endpoint: "/admin/beachLog/",
      },
    },
  },
};
export default CONSTANTS;

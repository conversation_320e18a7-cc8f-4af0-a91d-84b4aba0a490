import { AudioOutlined } from "@ant-design/icons";
import { Spin, Tooltip } from "antd";
import { useState } from "react";

export const AudioPlayer = ({ src }) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [audio, setAudio] = useState(null);

  const handlePlayAudio = () => {
    if (!src) return;

    if (audio) {
      // Pause the audio if it's currently playing
      audio.pause();
      setIsPlaying(false);
      return;
    }

    setIsLoading(true);
    const newAudio = new Audio(src);

    newAudio.play()
      .then(() => {
        setIsLoading(false);
        setIsPlaying(true);
        setAudio(newAudio);

        newAudio.onended = () => {
          setIsPlaying(false);
          setAudio(null); // Reset the audio object when playback is complete
        };
      })
      .catch(() => {
        setIsLoading(false);
        setIsPlaying(false);
      });
  };

  return (
    <Tooltip title={src ? "Play Pronunciation" : "No Pronunciation Found"}>
      {src ? (
        <span onClick={handlePlayAudio} className={`text-xl cursor-pointer`}>
          {isLoading ? (
            <Spin />
          ) : isPlaying ? (
            <AudioOutlined className="text-blue-500 animate-ping  " />
          ) : (
            <AudioOutlined className="text-blue-500" />
          )}
        </span>
      ) : (
        <AudioOutlined
          className={`text-xl cursor-not-allowed text-gray-500`}
        />
      )}
    </Tooltip>
  );
};

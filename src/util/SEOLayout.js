import React, { useEffect } from 'react';
import { Helmet } from 'react-helmet-async';

const SEOLayout = ({
  title,
  description,
  element,
  meta = [],
  canonicalUrl,
  ogType = 'website',
  ogImage,
  ogUrl,
  twitterCard = 'summary_large_image',
  twitterSite,
  twitterCreator,
  additionalMeta = [],
}) => {
  useEffect(() => {
    // Remove existing description meta tag
    const metaTags = document.querySelectorAll('meta[name="description"]');
    metaTags.forEach(tag => tag.remove());
  }, []);
  return (
    <>
      <Helmet>
        {/* Title */}
        {title && <title>{title}</title>}
        {/* <meta name="description" content="" /> */}
        {/* Meta Description */}
        {description && <meta name="description" content={description} />}
        
        {/* Canonical Link */}
        {canonicalUrl && <link rel="canonical" href={canonicalUrl} />}
        
        {/* Open Graph Tags */}
        {ogType && <meta property="og:type" content={ogType} />}
        {title && <meta property="og:title" content={title} />}
        {description && <meta property="og:description" content={description} />}
        {ogUrl && <meta property="og:url" content={ogUrl} />}
        {ogImage && <meta property="og:image" content={ogImage} />}
        
        {/* Twitter Card Tags */}
        {twitterCard && <meta name="twitter:card" content={twitterCard} />}
        {twitterSite && <meta name="twitter:site" content={twitterSite} />}
        {twitterCreator && <meta name="twitter:creator" content={twitterCreator} />}
        {title && <meta name="twitter:title" content={title} />}
        {description && <meta name="twitter:description" content={description} />}
        {ogImage && <meta name="twitter:image" content={ogImage} />}
        
        {/* Additional Meta Tags */}
        {meta.map(({ name, content }, i) => (
          <meta key={i} name={name} content={content} />
        ))}
        
        {/* Arbitrary Additional Meta Tags */}
        {additionalMeta.map(({ name, property, content }, i) => {
          if (name) return <meta key={i} name={name} content={content} />;
          if (property) return <meta key={i} property={property} content={content} />;
          return null;
        })}
      </Helmet>
      
      {/* Render the Element */}
      {element}
    </>
  );
};

export default SEOLayout;

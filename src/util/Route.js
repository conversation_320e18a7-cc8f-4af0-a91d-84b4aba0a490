import { <PERSON><PERSON><PERSON><PERSON>, BsSdCard } from "react-icons/bs";
import { <PERSON>a<PERSON><PERSON>ua<PERSON>, FaLevelUpAlt, FaUser } from "react-icons/fa";
import {
  MdCardGiftcard,
  MdGroup,
  MdQuiz,
  MdReport,
  MdSupport,
} from "react-icons/md";
import { VscDashboard } from "react-icons/vsc";
import { Navigate } from "react-router-dom";
import AppLayout from "../component/layout/AppLayout";
import CoursePage from "../page/app/course";
import SingleCoursePage from "../page/app/course/singleCourse";
import Dashboard from "../page/app/dashboard";
import FlashcardPage from "../page/app/flashcard";
import SingleFlashcardPage from "../page/app/flashcard/singleFlashcard";
import GroupPage from "../page/app/group";
import SingleGroupPage from "../page/app/group/singleGroup";
import LanguagePage from "../page/app/language";
import LevelPage from "../page/app/level";
import ProposecardPage from "../page/app/proposecard";
import QuizPage from "../page/app/quiz";
import QuestionPage from "../page/app/quiz/question";
import OptionPage from "../page/app/quiz/question/option";
import CourseReportPage from "../page/app/report/courseReport";
import FlashcardReportPage from "../page/app/report/flashcardReport";
import GroupReportPage from "../page/app/report/groupReport";
import SupportPage from "../page/app/support";
import UserPage from "../page/app/user";
import Error from "../page/error";
import UnAuthorize from "../page/unAuthorize";
import LogIn from "../page/user/login";
import { ROLES } from "./constant/dataConstants";
import LogsPage from "../page/app/logs";

export const loginRoot = "/";

export const appRoot = "/app";

export const ROUTES = {
  app: "/app",
  FORGOT_PASSWORD: "/forgot-password",
  DASHBOARD: "dashboard",

  USERS: "users",
  USERS_DETAIL: "users/:id",
  SETTINGS: "settings",
  CATEGORY: "category",
  FLASHCARD: "flashcard",
  QUIZ: "quiz",
  LANGUAGE: "language",
  LEVEL: "level",
  GROUP: "group",
  COURSE: "course",
  PROPOSECARD: "propose",
  REPORT: "report",
  SUPPORT: "support",
  LOGS: "logs",
};

// export const ROLES = {
//   ADMIN: "ADMIN",
//   DATA: "SUPERADMINNNNNN",
//   IMAGE: "image-person",
//   INTERN: "intern",
//   SEO: "seo",
//   SUPER_ADMIN: "SUPERADMIN",
//   SUPERUSER: "super-user",
//   // USER: "user",
//   WRITER: "writer",
//   DEFAULT: "default",
// };

const LOGIN_ROUTES = [
  {
    index: true,
    element: <LogIn />,
  },
];

const ALL_ROUTES = (appProps) => [
  ...LOGIN_ROUTES,

  {
    path: `${appRoot}`,
    element: <AppLayout {...appProps} />,
    Role: [
      ROLES.ADMIN,
      ROLES.DATA,
      ROLES.IMAGE,
      ROLES.INTERN,
      ROLES.SEO,
      ROLES.SUPER_ADMIN,
      ROLES.SUPERUSER,
      ROLES.WRITER,
      ROLES.DEFAULT,
    ],
    // label: "Dashboard ",

    children: [
      {
        index: true,
        element: (
          <Navigate
            to={`${ROUTES.DASHBOARD}`}
            {...appProps}
            appProps={appProps}
          />
        ),
      },
      {
        path: `${ROUTES.DASHBOARD}`,
        element: <Dashboard {...appProps} appProps={appProps} />,
        icon: <VscDashboard />,
        label: "Dashboard",
        Role: [
          ROLES.ADMIN,
          ROLES.DATA,
          ROLES.IMAGE,
          ROLES.INTERN,
          ROLES.SEO,
          ROLES.SUPER_ADMIN,
          ROLES.SUPERUSER,
          ROLES.WRITER,
          ROLES.DEFAULT,
        ],
        title: "Dashboard - Oroi App",
        description:
          "Get an overview of key metrics and insights on the Oroi Dashboard.",
        meta: [
          { name: "keywords", content: "dashboard, oroi, metrics, insights" },
        ],
      },
      {
        path: `${ROUTES.USERS}`,
        element: <UserPage {...appProps} appProps={appProps} />,
        icon: <FaUser />,
        label: "Users",
        Role: [
          ROLES.ADMIN,
          ROLES.DATA,
          ROLES.IMAGE,
          ROLES.INTERN,
          ROLES.SEO,
          ROLES.SUPER_ADMIN,
          ROLES.SUPERUSER,
          ROLES.WRITER,
          // ROLES.DEFAULT,
        ],
        title: "User Management - Oroi",
        description:
          "Manage user accounts, roles, and permissions effectively.",
        meta: [
          { name: "keywords", content: "user management, accounts, roles" },
        ],
      },
      {
        path: `${ROUTES.LANGUAGE}`,
        element: <LanguagePage {...appProps} appProps={appProps} />,
        icon: <FaLanguage />,
        label: "Languages",
        Role: [
          ROLES.ADMIN,
          ROLES.DATA,
          ROLES.IMAGE,
          ROLES.INTERN,
          ROLES.SEO,
          ROLES.SUPER_ADMIN,
          ROLES.SUPERUSER,
          ROLES.WRITER,
          // ROLES.DEFAULT,
        ],
        title: "Languages Overview - Oroi",
        description: "Explore and manage languages available on Oroi.",
        meta: [{ name: "keywords", content: "languages, multilingual, oroi" }],
      },
      {
        path: `${ROUTES.LEVEL}`,
        element: <LevelPage {...appProps} appProps={appProps} />,
        icon: <FaLevelUpAlt />,
        label: "Levels",
        Role: [
          ROLES.ADMIN,
          ROLES.DATA,
          ROLES.IMAGE,
          ROLES.INTERN,
          ROLES.SEO,
          ROLES.SUPER_ADMIN,
          ROLES.SUPERUSER,
          ROLES.WRITER,
          // ROLES.DEFAULT,
        ],
        title: "Difficulty Levels - Oroi",
        description: "Manage and customize difficulty levels for courses.",
        meta: [{ name: "keywords", content: "levels, difficulty, oroi" }],
      },
      {
        path: `${ROUTES.SUPPORT}`,
        element: <SupportPage {...appProps} appProps={appProps} />,
        icon: <MdSupport />,
        label: "Support",
        Role: [
          ROLES.ADMIN,
          ROLES.DATA,
          ROLES.IMAGE,
          ROLES.INTERN,
          ROLES.SEO,
          ROLES.SUPER_ADMIN,
          ROLES.SUPERUSER,
          ROLES.WRITER,
          // ROLES.DEFAULT,
        ],
        title: "Difficulty Support - Oroi",
        description: "Manage and customize difficulty Support for courses.",
        meta: [{ name: "keywords", content: "Support, difficulty, oroi" }],
      },

      {
        path: `${ROUTES.PROPOSECARD}`,
        element: <ProposecardPage {...appProps} appProps={appProps} />,
        icon: <MdCardGiftcard />,
        label: "Propose Cards",
        Role: [
          ROLES.ADMIN,
          ROLES.DATA,
          ROLES.IMAGE,
          ROLES.INTERN,
          ROLES.SEO,
          ROLES.SUPER_ADMIN,
          ROLES.SUPERUSER,
          ROLES.WRITER,
          // ROLES.DEFAULT,
        ],
        title: "Suggest New Cards - Oroi",
        description: "Propose new flashcards to enrich the Oroi database.",
        meta: [
          {
            name: "keywords",
            content: "propose, flashcards, oroi, suggestions",
          },
        ],
      },
      {
        path: `${ROUTES.FLASHCARD}`,
        icon: <BsSdCard />,
        label: "FlashCards",
        Role: [
          ROLES.ADMIN,
          ROLES.DATA,
          ROLES.IMAGE,
          ROLES.INTERN,
          ROLES.SEO,
          ROLES.SUPER_ADMIN,
          ROLES.SUPERUSER,
          ROLES.WRITER,
          // ROLES.DEFAULT,
        ],
        children: [
          {
            index: true,
            element: <FlashcardPage {...appProps} appProps={appProps} />,
            Role: [
              ROLES.ADMIN,
              ROLES.DATA,
              ROLES.IMAGE,
              ROLES.INTERN,
              ROLES.SEO,
              ROLES.SUPER_ADMIN,
              ROLES.SUPERUSER,
              ROLES.WRITER,
              // ROLES.DEFAULT,
            ],
            title: "Flashcards - Oroi",
            description:
              "View and manage your collection of flashcards on Oroi.",
            meta: [
              { name: "keywords", content: "flashcards, management, oroi" },
            ],
          },
          {
            path: `:flashcardId`,
            element: <SingleFlashcardPage {...appProps} />,
            Role: [
              ROLES.ADMIN,
              ROLES.DATA,
              ROLES.IMAGE,
              ROLES.INTERN,
              ROLES.SEO,
              ROLES.SUPER_ADMIN,
              ROLES.SUPERUSER,
              ROLES.WRITER,
              // ROLES.DEFAULT,
            ],
            title: "Flashcard Details - Oroi",
            description:
              "Explore the details and content of a specific flashcard.",
            meta: [
              { name: "keywords", content: "flashcard details, oroi, content" },
            ],
          },
        ],
      },
      {
        path: `${ROUTES.QUIZ}`,
        icon: <MdQuiz />,
        label: "Quiz",
        Role: [
          ROLES.ADMIN,
          ROLES.DATA,
          ROLES.IMAGE,
          ROLES.INTERN,
          ROLES.SEO,
          ROLES.SUPER_ADMIN,
          ROLES.SUPERUSER,
          ROLES.WRITER,
          // ROLES.DEFAULT,
        ],
        children: [
          {
            index: true,
            element: <QuizPage {...appProps} appProps={appProps} />,
            Role: [
              ROLES.ADMIN,
              ROLES.DATA,
              ROLES.IMAGE,
              ROLES.INTERN,
              ROLES.SEO,
              ROLES.SUPER_ADMIN,
              ROLES.SUPERUSER,
              ROLES.WRITER,
              // ROLES.DEFAULT,
            ],
            title: "Quiz - Oroi",
            description: "View and manage your collection of Quiz on Oroi.",
            meta: [{ name: "keywords", content: "Quiz, management, oroi" }],
          },
          {
            path: `:quizId`,
            children: [
              {
                index: true,
                element: <QuestionPage {...appProps} />,
                Role: [
                  ROLES.ADMIN,
                  ROLES.DATA,
                  ROLES.IMAGE,
                  ROLES.INTERN,
                  ROLES.SEO,
                  ROLES.SUPER_ADMIN,
                  ROLES.SUPERUSER,
                  ROLES.WRITER,
                  // ROLES.DEFAULT,
                ],
                title: "Question - Oroi",
                description:
                  "View and manage your collection of Question on Oroi.",
                meta: [
                  { name: "keywords", content: "Question, management, oroi" },
                ],
              },
              {
                path: `:questionId`,
                element: <OptionPage {...appProps} />,
                Role: [
                  ROLES.ADMIN,
                  ROLES.DATA,
                  ROLES.IMAGE,
                  ROLES.INTERN,
                  ROLES.SEO,
                  ROLES.SUPER_ADMIN,
                  ROLES.SUPERUSER,
                  ROLES.WRITER,
                  // ROLES.DEFAULT,
                ],
                title: "Option Details - Oroi",
                description:
                  "Explore the details and content of a specific option.",
                meta: [
                  {
                    name: "keywords",
                    content: "option details, oroi, content",
                  },
                ],
              },
            ],
            Role: [
              ROLES.ADMIN,
              ROLES.DATA,
              ROLES.IMAGE,
              ROLES.INTERN,
              ROLES.SEO,
              ROLES.SUPER_ADMIN,
              ROLES.SUPERUSER,
              ROLES.WRITER,
              // ROLES.DEFAULT,
            ],
            // title: "Flashcard Details - Oroi",
            // description: "Explore the details and content of a specific flashcard.",
            // meta: [{ name: "keywords", content: "flashcard details, oroi, content" }],
          },
        ],
      },

      {
        path: `${ROUTES.GROUP}`,
        icon: <MdGroup />,
        label: "Groups",
        Role: [
          ROLES.ADMIN,
          ROLES.DATA,
          ROLES.IMAGE,
          ROLES.INTERN,
          ROLES.SEO,
          ROLES.SUPER_ADMIN,
          ROLES.SUPERUSER,
          ROLES.WRITER,
          // ROLES.DEFAULT,
        ],
        children: [
          {
            index: true,
            element: <GroupPage {...appProps} appProps={appProps} />,
            Role: [
              ROLES.ADMIN,
              ROLES.DATA,
              ROLES.IMAGE,
              ROLES.INTERN,
              ROLES.SEO,
              ROLES.SUPER_ADMIN,
              ROLES.SUPERUSER,
              ROLES.WRITER,
              // ROLES.DEFAULT,
            ],
            title: "Groups - Oroi",
            description: "Organize and manage groups within the Oroi platform.",
            meta: [{ name: "keywords", content: "groups, organization, oroi" }],
          },
          {
            path: `:groupId`,
            element: <SingleGroupPage {...appProps} />,
            Role: [
              ROLES.ADMIN,
              ROLES.DATA,
              ROLES.IMAGE,
              ROLES.INTERN,
              ROLES.SEO,
              ROLES.SUPER_ADMIN,
              ROLES.SUPERUSER,
              ROLES.WRITER,
              // ROLES.DEFAULT,
            ],
            title: "Group Details - Oroi",
            description: "Detailed view and management of a specific group.",
            meta: [{ name: "keywords", content: "group details, oroi" }],
          },
        ],
      },

      {
        path: `${ROUTES.COURSE}`,
        icon: <BsBook />,
        label: "Courses",
        Role: [
          ROLES.ADMIN,
          ROLES.DATA,
          ROLES.IMAGE,
          ROLES.INTERN,
          ROLES.SEO,
          ROLES.SUPER_ADMIN,
          ROLES.SUPERUSER,
          ROLES.WRITER,
          // ROLES.DEFAULT,
        ],
        children: [
          {
            index: true,
            element: <CoursePage {...appProps} appProps={appProps} />,
            Role: [
              ROLES.ADMIN,
              ROLES.DATA,
              ROLES.IMAGE,
              ROLES.INTERN,
              ROLES.SEO,
              ROLES.SUPER_ADMIN,
              ROLES.SUPERUSER,
              ROLES.WRITER,
              // ROLES.DEFAULT,
            ],
            title: "Courses - Oroi",
            description: "Browse and manage the courses available on Oroi.",
            meta: [{ name: "keywords", content: "courses, management, oroi" }],
          },
          {
            path: `:courseId`,
            element: <SingleCoursePage {...appProps} />,
            Role: [
              ROLES.ADMIN,
              ROLES.DATA,
              ROLES.IMAGE,
              ROLES.INTERN,
              ROLES.SEO,
              ROLES.SUPER_ADMIN,
              ROLES.SUPERUSER,
              ROLES.WRITER,
              // ROLES.DEFAULT,
            ],
            title: "Course Details - Oroi",
            description: "View detailed information about a specific course.",
            meta: [{ name: "keywords", content: "course details, oroi" }],
          },
        ],
      },

      {
        path: `${ROUTES.REPORT}`,
        icon: <MdReport />,
        label: "Reports",
        Role: [
          ROLES.ADMIN,
          ROLES.DATA,
          ROLES.IMAGE,
          ROLES.INTERN,
          ROLES.SEO,
          ROLES.SUPER_ADMIN,
          ROLES.SUPERUSER,
          ROLES.WRITER,
          // ROLES.DEFAULT,
        ],
        children: [
          {
            index: true,
            element: <Navigate to={`${ROUTES.COURSE}`} />,
          },
          {
            path: `${ROUTES.COURSE}`,
            element: <CourseReportPage {...appProps} />,
            Role: [
              ROLES.ADMIN,
              ROLES.DATA,
              ROLES.IMAGE,
              ROLES.INTERN,
              ROLES.SEO,
              ROLES.SUPER_ADMIN,
              ROLES.SUPERUSER,
              ROLES.WRITER,
              // ROLES.DEFAULT,
            ],
            icon: <BsBook />,
            label: "Course Reports",
            title: "Course Reports - Oroi",
            description: "View reports related to courses",
            meta: [{ name: "keywords", content: "course reports, analytics" }],
          },
          {
            path: `${ROUTES.FLASHCARD}`,
            element: <FlashcardReportPage {...appProps} />,
            Role: [
              ROLES.ADMIN,
              ROLES.DATA,
              ROLES.IMAGE,
              ROLES.INTERN,
              ROLES.SEO,
              ROLES.SUPER_ADMIN,
              ROLES.SUPERUSER,
              ROLES.WRITER,
              // ROLES.DEFAULT,
            ],
            icon: <BsSdCard />,
            label: "Flashcard Reports",
            title: "Flashcard Reports - Oroi",
            description: "View reports related to flashcards",
            meta: [
              { name: "keywords", content: "flashcard reports, analytics" },
            ],
          },
          {
            path: `${ROUTES.GROUP}`,
            element: <GroupReportPage {...appProps} />,
            Role: [
              ROLES.ADMIN,
              ROLES.DATA,
              ROLES.IMAGE,
              ROLES.INTERN,
              ROLES.SEO,
              ROLES.SUPER_ADMIN,
              ROLES.SUPERUSER,
              ROLES.WRITER,
              // ROLES.DEFAULT,
            ],
            icon: <MdGroup />,
            label: "Group Reports",
            title: "Group Reports - Oroi",
            description: "View reports related to groups",
            meta: [{ name: "keywords", content: "group reports, analytics" }],
          },
        ],
      },
      {
        path: `${ROUTES.LOGS}`,
        element: <LogsPage {...appProps} />,
        Role: [
          ROLES.ADMIN,

          ROLES.SUPER_ADMIN,
          ROLES.SUPERUSER,

          // ROLES.DEFAULT,
        ],
        icon: <MdReport />,
        label: "Logs",
        title: "Logs - Oroi",
        description: "View logs related to the Oroi platform",
      },
    ],
  },
  {
    path: "/error",
    element: <Error {...appProps} />,
    title: "404 - Page Not Found",
    description:
      "The page you are looking for might have been removed or is temporarily unavailable.",
    meta: [{ name: "keywords", content: "404, not found, error" }],
  },
  {
    path: "/unAuthorize",
    element: <UnAuthorize {...appProps} />,
  },
  {
    path: "*",
    element: <Navigate to="/error" {...appProps} />,
  },
];

export default ALL_ROUTES;

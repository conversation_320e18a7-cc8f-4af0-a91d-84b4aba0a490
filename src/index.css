/* globals.css */
@tailwind base;
@tailwind components;
@tailwind utilities;
@import "antd/dist/reset.css";

/* // fix Tailwind CSS border styles,form Tailwind CSS's preflight */
*,
::before,
::after {
  /* box-sizing: border-box; 1 */
  border-width: 0; /* 2 */
  border-style: solid; /* 2 */
  border-color: theme("borderColor.DEFAULT", currentColor); /* 2 */
}

::before,
::after {
  --tw-content: "";
}

p,
h1,
h2,
h3,
h4,
h5,
h6 {
  margin: 0;
  padding: 0;
}

.form {
  margin: 0 !important;
  /* margin-bottom: 10px !important; */
}

.custom-tab-container .ant-tabs-tab {
  /* Styles to make all tabs look like buttons */
  border: 1px solid #d9d9d9;
  border-radius: 2px;
  padding: 0 !important;
  margin: 0 !important;
  /* margin-right: 8px; */
  /* padding: 4px 15px; */
  background-color: #f5f5f5;
  transition: all 0.3s ease;
}

/* Additional styles for the 'Project' tab to stand out */
.custom-tab-container .ant-tabs-tab .custom-tab-button {
  /* Your specific button styles for 'Project' tab */
  background-color: blue; /* Example color */
  color: white;
  border-radius: 4px;
  padding: 10px 20px !important;
  /* Add any hover effects or additional styling you want here */
}

.custom-tab-container .ant-tabs-tab .custom-tab-button:hover {
  /* Hover styles if needed */
  background-color: darkblue; /* Darker blue on hover */
  color: #fff;
}

.konvajs-content {
  min-height: 400px !important;
}

.map-container {
  height: 700px;
  width: 100%;
}

.sidebar {
  background-color: rgb(35 55 75 / 90%);
  color: #fff;
  padding: 6px 12px;
  font-family: monospace;
  z-index: 1;
  position: absolute;
  top: 0;
  left: 0;
  margin: 12px;
  border-radius: 4px;
}

.marker {
  background-image: url("https://beachnearby.com/marker.svg");
  background-size: cover;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  cursor: pointer;
}
/* @keyframes countUp {
  from { content: attr(data-from); }
  to { content: attr(data-to); }
}

.stat-value::after {
  animation: countUp 10s forwards;
  content: attr(data-to);
  display: inline-block;
} */
@keyframes countUp {
  from {
    transform: scale(0.8);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

.stat-count {
  animation: countUp 2s ease-out;
}

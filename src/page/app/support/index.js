import { Breadcrumb, Row } from "antd";
import React from "react";
import CRUDComponent from "../../../component/common/CRUD-Component";
import CONSTANTS from "../../../util/constant/CONSTANTS";

const SupportPage = (props) => {
  return (
    <>
      <Breadcrumb
        className="mx-2 mb-3 mt-6"
        separator=" > "
        items={[
          {
            title: "Home",
          },
          {
            // href: `${appRoot}/${ROUTES.Support}`,
            title: "Support",
          },
        ]}
      />
      <Row>
        <CRUDComponent
          GET={{
            API: CONSTANTS.API.Support.getAll,
            DataModifier: (res, API, Setrefresh) => {
              return res?.map((el) => ({
                ...el,
              }));
            },
            column: CONSTANTS.TABLE.SUPPORT,
          }}
          // UPDATE={{
          //   API: CONSTANTS.API.Support.update,
          //   message: "Updated Support successfully",
          //   modaltitle: "Update Support",
          //   modalFields: CONSTANTS.FORM_FIELD.LEVEL,
          //   // payloadModifier: (res) => res,
          // }}
          // CREATE={
          //   [
          //     ROLES.ADMIN,
          //     ROLES.DATA,
          //     // ROLES.IMAGE,
          //     // ROLES.INTERN,
          //     // ROLES.SEO,
          //     ROLES.SUPER_ADMIN,
          //     // ROLES.SUPERUSER,
          //     // ROLES.WRITER,
          //   ].some((el) => el === props?.profile?.role) && {
          //     name: "Add a Support",
          //     API: CONSTANTS.API.Support.create,
          //     message: "Created Support successfully",
          //     modaltitle: "Add Support",
          //     modalFields: CONSTANTS.FORM_FIELD.LEVEL,
          //   }
          // }
          DELETE={{
            API: CONSTANTS.API.Support.delete,
            message: "Deleted Successfully",
            confirm_message: "Are you sure to Delete this Support?",
          }}
          // FILTERSORTKEY={{
          //   cityName: "$city.name$",
          // }}
          datefilter
          isSearch
          selectionOff
          props={props}
        />
      </Row>
    </>
  );
};

export default SupportPage;

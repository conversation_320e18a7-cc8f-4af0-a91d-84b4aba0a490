import { Breadcrumb, <PERSON> } from "antd";
import React from "react";
import CRUDComponent from "../../../../component/common/CRUD-Component";
import CONSTANTS from "../../../../util/constant/CONSTANTS";


const GroupReportPage = (props) => {
  return (
    <>
      <Breadcrumb
        className="mx-2 mb-3 mt-6"
        separator=" > "
        items={[
          {
            title: "Home",
          },
          {
            // href: `${appRoot}/${ROUTES.GroupReport}`,
            title: "Group Report",
          },
        ]}
      />
      <Row>
        <CRUDComponent
          GET={{
            API: CONSTANTS.API.GroupReport.getAll,
            DataModifier: (res, API, Setrefresh) => {
              return res?.map((el) => ({
                ...el,
              }));
            },
            column: CONSTANTS.TABLE.REPORT,
          }}
        //   UPDATE={{
        //     API: CONSTANTS.API.GroupReport.update,
        //     message: "Updated Group Report successfully",
        //     modaltitle: "Update Group Report",
        //     modalFields: CONSTANTS.FORM_FIELD.LEVEL,
        //     // payloadModifier: (res) => res,
        //   }}
        //   CREATE={
        //     [
        //       ROLES.ADMIN,
        //       ROLES.DATA,
        //       // ROLES.IMAGE,
        //       // ROLES.INTERN,
        //       // ROLES.SEO,
        //       ROLES.SUPER_ADMIN,
        //       // ROLES.SUPERUSER,
        //       // ROLES.WRITER,
        //     ].some((el) => el === props?.profile?.role) && {
        //       name: "Add a GroupReport",
        //       API: CONSTANTS.API.GroupReport.create,
        //       message: "Created GroupReport successfully",
        //       modaltitle: "Add GroupReport",
        //       modalFields: CONSTANTS.FORM_FIELD.LEVEL,
        //     }
        //   }
          DELETE={{
            API: CONSTANTS.API.GroupReport.delete,
            message: "Deleted Successfully",
            confirm_message: "Are you sure to Delete this Group Report?",
          }}
          // FILTERSORTKEY={{
          //   cityName: "$city.name$",
          // }}
          datefilter
          isSearch
          selectionOff
          props={props}
        />
      </Row>
    </>
  );
};

export default GroupReportPage;

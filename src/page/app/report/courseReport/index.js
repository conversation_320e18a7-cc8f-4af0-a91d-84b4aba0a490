import { Breadcrumb, <PERSON> } from "antd";
import React from "react";
import CRUDComponent from "../../../../component/common/CRUD-Component";
import CONSTANTS from "../../../../util/constant/CONSTANTS";


const CourseReportPage = (props) => {
  return (
    <>
      <Breadcrumb
        className="mx-2 mb-3 mt-6"
        separator=" > "
        items={[
          {
            title: "Home",
          },
          {
            // href: `${appRoot}/${ROUTES.CourseReport}`,
            title: "Course Report",
          },
        ]}
      />
      <Row>
        <CRUDComponent
          GET={{
            API: CONSTANTS.API.CourseReport.getAll,
            DataModifier: (res, API, Setrefresh) => {
              return res?.map((el) => ({
                ...el,
              }));
            },
            column: CONSTANTS.TABLE.REPORT,
          }}
        //   UPDATE={{
        //     API: CONSTANTS.API.CourseReport.update,
        //     message: "Updated Course Report successfully",
        //     modaltitle: "Update Course Report",
        //     modalFields: CONSTANTS.FORM_FIELD.LEVEL,
        //     // payloadModifier: (res) => res,
        //   }}
        //   CREATE={
        //     [
        //       ROLES.ADMIN,
        //       ROLES.DATA,
        //       // ROLES.IMAGE,
        //       // ROLES.INTERN,
        //       // ROLES.SEO,
        //       ROLES.SUPER_ADMIN,
        //       // ROLES.SUPERUSER,
        //       // ROLES.WRITER,
        //     ].some((el) => el === props?.profile?.role) && {
        //       name: "Add a CourseReport",
        //       API: CONSTANTS.API.CourseReport.create,
        //       message: "Created CourseReport successfully",
        //       modaltitle: "Add CourseReport",
        //       modalFields: CONSTANTS.FORM_FIELD.LEVEL,
        //     }
        //   }
          DELETE={{
            API: CONSTANTS.API.CourseReport.delete,
            message: "Deleted Successfully",
            confirm_message: "Are you sure to Delete this Course Report?",
          }}
          // FILTERSORTKEY={{
          //   cityName: "$city.name$",
          // }}
          datefilter
          isSearch
          selectionOff
          props={props}
        />
      </Row>
    </>
  );
};

export default CourseReportPage;

import { Breadcrumb, <PERSON> } from "antd";
import React from "react";
import CRUDComponent from "../../../../component/common/CRUD-Component";
import CONSTANTS from "../../../../util/constant/CONSTANTS";


const FlashcardReportPage = (props) => {
  return (
    <>
      <Breadcrumb
        className="mx-2 mb-3 mt-6"
        separator=" > "
        items={[
          {
            title: "Home",
          },
          {
            // href: `${appRoot}/${ROUTES.FlashcardReport}`,
            title: "Flashcard Report",
          },
        ]}
      />
      <Row>
        <CRUDComponent
          GET={{
            API: CONSTANTS.API.FlashcardReport.getAll,
            DataModifier: (res, API, Setrefresh) => {
              return res?.map((el) => ({
                ...el,
              }));
            },
            column: CONSTANTS.TABLE.REPORT,
          }}
        //   UPDATE={{
        //     API: CONSTANTS.API.FlashcardReport.update,
        //     message: "Updated Flashcard Report successfully",
        //     modaltitle: "Update Flashcard Report",
        //     modalFields: CONSTANTS.FORM_FIELD.LEVEL,
        //     // payloadModifier: (res) => res,
        //   }}
        //   CREATE={
        //     [
        //       ROLES.ADMIN,
        //       ROLES.DATA,
        //       // ROLES.IMAGE,
        //       // ROLES.INTERN,
        //       // ROLES.SEO,
        //       ROLES.SUPER_ADMIN,
        //       // ROLES.SUPERUSER,
        //       // ROLES.WRITER,
        //     ].some((el) => el === props?.profile?.role) && {
        //       name: "Add a FlashcardReport",
        //       API: CONSTANTS.API.FlashcardReport.create,
        //       message: "Created FlashcardReport successfully",
        //       modaltitle: "Add FlashcardReport",
        //       modalFields: CONSTANTS.FORM_FIELD.LEVEL,
        //     }
        //   }
          DELETE={{
            API: CONSTANTS.API.FlashcardReport.delete,
            message: "Deleted Successfully",
            confirm_message: "Are you sure to Delete this Flashcard Report?",
          }}
          // FILTERSORTKEY={{
          //   cityName: "$city.name$",
          // }}
          datefilter
          isSearch
          selectionOff
          props={props}
        />
      </Row>
    </>
  );
};

export default FlashcardReportPage;

import { Breadcrumb, Row } from "antd";
import React from "react";
import CRUDComponent from "../../../component/common/CRUD-Component";
import CONSTANTS from "../../../util/constant/CONSTANTS";
import { ROLES } from "../../../util/constant/dataConstants";

const UserPage = (props) => {
  return (
    <>
      <Breadcrumb
        className="mx-2 mb-3 mt-6"
        separator=" > "
        items={[
          {
            title: "Home",
          },
          {
            // href: `${appRoot}/${ROUTES.User}`,
            title: "User",
          },
        ]}
      />
      <Row>
        <CRUDComponent
          GET={{
            API: CONSTANTS.API.User.getAll,
            DataModifier: (res, API, Setrefresh) => {
              return res?.map((el) => ({
                ...el,
                status: {
                  checked: !el?.isBlocked,
                  id: el.id,
                  onClick: (id, val) => {
                    const UpdateAPIEnd = { ...CONSTANTS?.API.User.update };
                    UpdateAPIEnd.endpoint = `${UpdateAPIEnd?.endpoint}${id}`;
                    API.sendRequest(
                      UpdateAPIEnd,
                      () => {
                        Setrefresh((prev) => !prev);
                      },
                      { isBlocked: val },
                      `User ${
                        val ? "blocked" : "un blocked"
                      } Successfully`,
                      () => {},
                      "Currently Not working We will update Soon"
                    );
                  },
                },
              }));
            },
            column: CONSTANTS.TABLE.USERS,
          }}
          UPDATE={{
            API: CONSTANTS.API.User.update,
            message: "Updated User successfully",
            modaltitle: "Update User",
            modalFields: CONSTANTS.FORM_FIELD.USERS_MODAL,
            // payloadModifier: (res) => res,
          }}
          CREATE={
            [
              ROLES.ADMIN,
              ROLES.DATA,
              // ROLES.IMAGE,
              // ROLES.INTERN,
              // ROLES.SEO,
              ROLES.SUPER_ADMIN,
              // ROLES.SUPERUSER,
              // ROLES.WRITER,
            ].some((el) => el === props?.profile?.role) && {
              name: "Add a User",
              API: CONSTANTS.API.User.create,
              message: "Created User successfully",
              modaltitle: "Add User",
              modalFields: CONSTANTS.FORM_FIELD.USERS_MODAL,
            }
          }
          DELETE={{
            API: CONSTANTS.API.User.delete,
            message: "Deleted Successfully",
            confirm_message: "Are you sure to Delete this User?",
          }}
          // FILTERSORTKEY={{
          //   cityName: "$city.name$",
          // }}
          datefilter
          // isSearch
          selectionOff
          props={props}
        />
      </Row>
    </>
  );
};

export default UserPage;

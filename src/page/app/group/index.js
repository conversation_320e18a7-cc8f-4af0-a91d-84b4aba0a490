import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Row } from "antd";
import React, { useCallback, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import CRUDComponent from "../../../component/common/CRUD-Component";
import useHttp from "../../../hooks/use-http";
import CONSTANTS from "../../../util/constant/CONSTANTS";
import { ROLES } from "../../../util/constant/dataConstants";

const GroupPage = (props) => {
  const API = useHttp();
  useEffect(() => {
    API.sendRequest(
      CONSTANTS.API.Language.getAll,
      (res) => {
        const LangugesArr = res?.data?.rows?.map((el) => ({
          ...el,
          label: ` ${el?.name} ( ${el?.code} - ${el?.englishName})`,
          // label: `${el?.code} - ${el?.name} ( ${el?.englishName})`,
          value: el?.id,
        }));
        CONSTANTS.FORM_FIELD.FLASHCARD.find(
          (el) => el?.id === "languageId"
        ).option = LangugesArr;
        CONSTANTS.FORM_FIELD.GROUP.find(
          (el) => el?.id === "languageId"
        ).option = LangugesArr;
        CONSTANTS.FORM_FIELD.COURSE.find(
          (el) => el?.id === "languageId"
        ).option = LangugesArr;
      },
      { isActive: true }
    );
  }, []);
  const navigate = useNavigate();
  const handleNavigate = useCallback(
    (id) => {
      // console.log(`Navigating to ${id}`);
      navigate(`${id}`);
    },
    [navigate]
  );
  return (
    <>
      <Breadcrumb
        className="mx-2 mb-3 mt-6"
        separator=" > "
        items={[
          {
            title: "Home",
          },
          {
            // href: `${appRoot}/${ROUTES.Group}`,
            title: "Group",
          },
        ]}
      />
      <Row>
        <CRUDComponent
          GET={{
            API: CONSTANTS.API.Group.getAll,
            DataModifier: (res, API, Setrefresh) => {
              return res?.map((el) => ({
                ...el,
                // no: `${el?.id}`.padStart(6, "0"),
                view: {
                  onClick: () => {
                    // if (el?.role === "user") {
                    //   return notification.info({
                    //     message: "This Section is not available to user",
                    //   });
                    // }
                    // navigate(`${el?.id}`);
                    handleNavigate(el?.id);
                  },
                  id: el?.id,
                },
                status: {
                  checked: el?.isActive,
                  id: el.id,
                  onClick: (id, val) => {
                    const UpdateAPIEnd = { ...CONSTANTS?.API.Group.update };
                    UpdateAPIEnd.endpoint = `${UpdateAPIEnd?.endpoint}${id}`;
                    API.sendRequest(
                      UpdateAPIEnd,
                      () => {
                        Setrefresh((prev) => !prev);
                      },
                      { isActive: !val },
                      `Group ${
                        !val ? "activated" : "deactivated"
                      } Successfully`,
                      () => {},
                      "Currently Not working We will update Soon"
                    );
                  },
                },
                premium: {
                  checked: el?.isPremium,
                  id: el.id,
                  onClick: (id, val) => {
                    const UpdateAPIEnd = { ...CONSTANTS?.API.Group.update };
                    UpdateAPIEnd.endpoint = `${UpdateAPIEnd?.endpoint}${id}`;
                    API.sendRequest(
                      UpdateAPIEnd,
                      () => {
                        Setrefresh((prev) => !prev);
                      },
                      { isPremium: !val },
                      `Group ${
                        !val ? "primiumed" : "de-premiumed"
                      } Successfully`,
                      () => {},
                      "Currently Not working We will update Soon"
                    );
                  },
                },
              }));
            },
            column: CONSTANTS.TABLE.GROUP,
          }}
          UPDATE={{
            API: CONSTANTS.API.Group.update,
            message: "Updated Group successfully",
            modaltitle: "Update Group",
            modalFields: CONSTANTS.FORM_FIELD.GROUP,
            isFormData: true,
            // payloadModifier: (res) => res,
          }}
          CREATE={
            [
              ROLES.ADMIN,
              ROLES.DATA,
              // ROLES.IMAGE,
              // ROLES.INTERN,
              // ROLES.SEO,
              ROLES.SUPER_ADMIN,
              // ROLES.SUPERUSER,
              // ROLES.WRITER,
            ].some((el) => el === props?.profile?.role) && {
              name: "Add a Group",
              API: CONSTANTS.API.Group.create,
              message: "Created Group successfully",
              modaltitle: "Add Group",
              modalFields: CONSTANTS.FORM_FIELD.GROUP,
              isFormData: true,
            }
          }
          DELETE={{
            API: CONSTANTS.API.Group.delete,
            message: "Deleted Successfully",
            confirm_message: "Are you sure to Delete this Group?",
          }}
          FILTERSORTKEY={{
            language: "Language.name",
          }}
          datefilter
          isSearch
          selectionOff
          props={props}
        />
      </Row>
    </>
  );
};

export default GroupPage;

import { <PERSON>readcrumb, <PERSON> } from "antd";
import React, { useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import FlashCardSearchandAdd from "../../../../component/common/card/FlashCardSearchandAdd";
import CRUDComponent from "../../../../component/common/CRUD-Component";
import CONSTANTS from "../../../../util/constant/CONSTANTS";
import { appRoot, ROUTES } from "../../../../util/Route";

const SingleGroupPage = (props) => {
  const paramas = useParams();
  const [extraQuery, setExtraQuery] = useState({ ...paramas });

  const navigate = useNavigate();

  const handleBreadcrumbClick = (route) => {
    navigate(route);
  };

  return (
    <>
      <Breadcrumb
        className="mx-2 mb-3 mt-6"
        separator=" > "
        items={[
          {
            title: "Home",
          },
          {
            onClick: () => handleBreadcrumbClick(`${appRoot}/${ROUTES.GROUP}`),
            title: "Groups",
          },
          {
            title: "GroupFlashcard",
          },
        ]}
        params={{}}
      />
      <Row>
        <CRUDComponent
          GET={{
            // extraQuery: {
            //   groupId: paramas?.groupId,
            // },
            extraQuery,
            API: CONSTANTS.API.GroupFlashcard.getAll,
            DataModifier: (res, API, Setrefresh) => {
              return res?.map((el) => ({
                ...el?.flashcard,
                ...el,
                // status: {
                //   checked: el?.isActive,
                //   id: el.id,
                //   onClick: (id, val) => {
                //     const UpdateAPIEnd = {
                //       ...CONSTANTS?.API.GroupFlashcard.update,
                //     };
                //     UpdateAPIEnd.endpoint = `${UpdateAPIEnd?.endpoint}${id}`;
                //     API.sendRequest(
                //       UpdateAPIEnd,
                //       () => {
                //         Setrefresh((prev) => !prev);
                //       },
                //       { isActive: !val },
                //       `GroupFlashcard ${
                //         !val ? "activated" : "deactivated"
                //       } Successfully`,
                //       () => {},
                //       "Currently Not working We will update Soon"
                //     );
                //   },
                // },
              }));
            },
            column: CONSTANTS.TABLE.GROUP_FLASHCARD,
          }}
          // UPDATE={{
          //   API: CONSTANTS.API.GroupFlashcard.update,
          //   message: "Updated GroupFlashcard successfully",
          //   modaltitle: "Update GroupFlashcard",
          //   modalFields: CONSTANTS.FORM_FIELD.GROUP_FLASHCARD,
          //   // payloadModifier: (res) => {
          //   //     console.log(res)
          //   //     return res
          //   // },
          // }}
          // CREATE={
          //   [
          //     ROLES.ADMIN,
          //     ROLES.DATA,
          //     // ROLES.IMAGE,
          //     // ROLES.INTERN,
          //     // ROLES.SEO,
          //     ROLES.SUPER_ADMIN,
          //     // ROLES.SUPERUSER,
          //     // ROLES.WRITER,
          //   ].some((el) => el === props?.profile?.role) && {
          //     name: "Add a GroupFlashcard",
          //     API: CONSTANTS.API.GroupFlashcard.create,
          //     message: "Created GroupFlashcard successfully",
          //     modaltitle: "Add GroupFlashcard",
          //     modalFields: CONSTANTS.FORM_FIELD.GROUP_FLASHCARD,
          //     payloadModifier: (res) => {
          //       console.log(res)
          //       const payload = {...res , groupId :paramas?.groupId}
          //       return payload
          //   },
          //   // AddAfterAddButton: (
          //   //     <>
          //   //     <FlashCardModalwithId groupId={paramas?.groupId} />
          //   //     </>
          //   //   ),
          //   }
          // }
          DELETE={{
            API: CONSTANTS.API.GroupFlashcard.delete,
            message: "Deleted Successfully",
            confirm_message: "Are you sure to Delete this GroupFlashcard?",
          }}
          // FILTERSORTKEY={{
          //   cityName: "$city.name$",
          // }}
          // isSearch
          selectionOff
          props={props}
          AddOnComponent={
            <>
              <FlashCardSearchandAdd
                optionmodifier={(option) => {
                  return {
                    id: option?.id,
                    name: `${option?.word} - ${option?.pronunciation}`,
                    description: `${option?.example}`,
                  };
                }}
                payloadmodifier={(pay) => {
                  return {
                    groupId: paramas?.groupId,
                    flashcardId: pay?.id,
                    position: 100,
                  };
                }}
                endpointObject={CONSTANTS.API.GroupFlashcard.create}
                refresher={() => {
                  setExtraQuery((prev) => ({ ...prev }));
                }}
              />
            </>
          }
        />
      </Row>
    </>
  );
};

export default SingleGroupPage;

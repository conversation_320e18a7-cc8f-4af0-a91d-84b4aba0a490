import { Column } from "@ant-design/plots";
import { Alert } from "antd";
import React from "react";

const ColumnChart = ({ data,loading }) => {
  const config = {
    data,
    xField: "type",
    yField: "value",

    maxColumnWidth: 10,
    style: {
      radiusTopLeft: 10,
      radiusTopRight: 10,
      maxColumnWidth: 10,
      maxWidth: 10,
    },
    legend: false,
    scrollbar: {
      x: {
        ratio: 128 / data?.length,
      },
    },
    scale: {
      y: {
        type: "linear",
      },
    },
  };
  return loading ?  "Loading..." : data?.length?<Column {...config} /> : <Alert type="info" description="No data found"  />;
};

export default ColumnChart;

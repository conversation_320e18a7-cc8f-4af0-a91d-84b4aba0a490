import { Card, Col } from "antd";
import React, { useEffect, useState } from "react";
import useHttp from "../../../hooks/use-http";
import CONSTANTS from "../../../util/constant/CONSTANTS";
import Bar<PERSON>hart from "./BarChart";
import Column<PERSON>hart from "./ColumnChart";

const Graphs = () => {
  const [data, setData] = useState({});
  const [Bardata, setBardata] = useState([]);
  const API = useHttp();
  useEffect(() => {
    API.sendRequest(CONSTANTS.API.Dashboard.graph, (res) => {
      setData(res?.data || {});
    });
    API.sendRequest(CONSTANTS.API.Dashboard.Flashcards, (res) => {
      // setBardata( transformDataForChart(res?.data?.languageWise      ))
      console.log(res?.data);
    });
    API.sendRequest(CONSTANTS.API.Dashboard.languageWise, (res) => {
      setBardata(transformDataForChart(res?.data?.languageWise));
      console.log(res?.data);
    });
  }, []);
  return (
    <>
      <Col md={12}>
        <Card className="h-full w-full">
          <ColumnChart
            loading={API?.isLoading}
            data={
              data?.dailyUserCounts?.map((el) => ({
                ...el,
                type: el?.date,
                value: el?.userCount,
              })) ?? []
            }
          />
        </Card>
      </Col>
      <Col md={12}>
        <Card className="h-full w-full">
          <ColumnChart
            loading={API?.isLoading}
            data={
              data?.dailyProposedCardCounts?.map((el) => ({
                ...el,
                type: el?.date,
                value: el?.proposedCardCount,
              })) ?? []
            }
          />
        </Card>
      </Col>
      <Col span={24}>
        <Card className="h-full w-full">
          <BarChart loading={API?.isLoading} data={Bardata} />
        </Card>
      </Col>
    </>
  );
};

export default Graphs;
export function transformDataForChart(data) {
  const transformedData = [];
  // console.log(data);
  data.forEach((item) => {
    if (item.courses || item.flashcards || item.proposedCards || item.groups ) {
      transformedData.push({
        language: item.language,
        metric: "Courses",
        count: item.courses,
      });
   
      transformedData.push({
        language: item.language,
        metric: "Flashcards",
        count: item.flashcards,
      });
   
      transformedData.push({
        language: item.language,
        metric: "Proposed Cards",
        count: item.proposedCards,
      });
  
      transformedData.push({
        language: item.language,
        metric: "Groups",
        count: item.groups,
      });
    }

  });

  return transformedData;
}

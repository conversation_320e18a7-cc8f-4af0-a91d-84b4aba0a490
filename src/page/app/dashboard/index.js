import { Col, Row, Spin } from "antd";
import React from "react";
import { ROLES } from "../../../util/constant/dataConstants";
import Analytics from "./Analytics";
import Graphs from "./Graphs";

const Dashboard = (props) => {
  if ([ROLES.DEFAULT].some((el) => el === props?.profile?.role)) {
    return (
      <>
        {" "}
        <Row
          gutter={[16, 16]}
          className="flex justify-center items-center h-full"
        >
          {" "}
          <Spin size="large" fullscreen />{" "}
        </Row>{" "}
      </>
    );
  }
  return (
    <Row gutter={[16, 32]} className="flex justify-center items-center h-full">
      <Col xs={24}>
        <Analytics />
      </Col>
      <Graphs />
    </Row>
  );
};

export default Dashboard;

import { Column } from '@ant-design/charts';

const BarChart = ({ data = [],loading }) => {
    const config = {
        data,
        xField: 'language',
        yField: 'count',
        colorField: 'metric',
        group: true,
        xAxis: {
            label: {
                // Rotate the labels on the x-axis
                // autoRotate: false,
                rotate: 0,
                // Truncate long labels
                formatter: (text) => {
                    const maxLength = 5; // Set max length
                    return text.length > maxLength ? `${text.substring(0, maxLength)}...` : text;
                },
            },
        },
        // xAxis: {
        //     // label: {
        //     //   formatter: (v) => {
        //     //     console.log(v)
        //     //     return`${v.substring(0, 5)}...`}
        //     // },
        //     labelFormatter :(v) => {
        //         console.log(v)
        //         return`${v.substring(0, 5)}...`},
        //         tickFormatter:(v) => {
        //             console.log(v)
        //             return`${v.substring(0, 5)}...`}
        //   },
        //   label: {
        //     position: "top",
        //     offsetY: 12, // adjust offset
        //     formatter: ({ value }) => {
        //         console.log(value)
        //         return value}
        //   },
        style: {
          // 矩形四个方向的内边距
          inset: 5,
          // 矩形单个方向的内边距
          // insetLeft:5,
          // insetRight:20,
          // insetBottom:10
          // insetTop:10
        },
        // scrollbar: {
        //     x: {
        //       ratio: 0.05,
        //     },
        //   },
          slider: {
            x: {
              values: [0.1, 0.5],
            },
          },
      };

    return loading ?"Loading...": data.length? <Column {...config} /> :"preparing..."; 
};

export default BarChart;
// import { Column } from '@ant-design/charts';

// const BarChart = ({ data = [] }) => {
//     const config = {
//         data,
//         xField: 'language',
//         yField: 'count',
//         seriesField: 'metric',
//         isGroup: true,
//         // colorField: 'name',
//         // color: ['#6394f8', '#62daaa', '#657798', '#f6c022'],
//         // Responsive settings
//         autoFit: true, // Automatically adjusts chart size based on the container
//         padding: 'auto', // Automatically adjust padding
//         xAxis: {
//             label: {
//                 autoHide: true, // Hide labels when they overlap
//                 autoRotate: true, // Rotate labels to prevent overlap
//                 style: {
//                     fontSize: 10, // Adjust font size for better readability
//                 },
//             },
//             title: {
//                 text: 'Languages',
//                 style: {
//                     fontSize: 32,
//                 },
//             },
//         },
//         yAxis: {
//             title: {
//                 text: 'Count',
//                 style: {
//                     fontSize: 20,
//                 },
//             },
//             minLimit: 0, // Minimum value on y-axis to prevent negative values
//         },
//         legend: {
//             position: 'top', // Move legend to the top to save horizontal space
//             itemName: {
//                 style: {
//                     fontSize: 20,
//                 },
//             },
//         },
//         scrollbar: {
//             type: 'horizontal', // Add a horizontal scrollbar
//         },
//         interactions: [
//             { type: 'element-active' }, // Highlight bars on hover for better interactivity
//         ],
//     };

//     return <Column {...config} />;
// };

// export default BarChart;

// import { Bar } from '@ant-design/charts';

// const BarChart = ({data =[]}) => {
//     const config = {
//         data,
//         xField: 'language',
//         yField: 'count',
//         seriesField: 'metric',
//         isGroup: true,
//         color: ['#6394f8', '#62daaa', '#657798', '#f6c022'],
//     };
//   return (
//     <Bar {...config} />
//   )
// }

// export default BarChart
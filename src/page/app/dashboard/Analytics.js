import { <PERSON>, <PERSON>, Collapse, <PERSON>, <PERSON>, Statistic, Typography } from "antd";
import React, { useEffect, useState } from "react";
import {
  <PERSON>aB<PERSON>,
  <PERSON>a<PERSON>ook<PERSON><PERSON>,
  FaCheck,
  FaClock,
  FaLanguage,
  FaLayerGroup,
  FaRegHandPaper,
  FaTimes,
  FaUser,
  FaUsers,
} from "react-icons/fa";
import { Md<PERSON><PERSON>, MdFlashOn, MdPersonOutline } from "react-icons/md";
import useHttp from "../../../hooks/use-http";
import CONSTANTS from "../../../util/constant/CONSTANTS";
import { ROLES } from "../../../util/constant/dataConstants";

const { Title } = Typography;
const { Panel } = Collapse;

// Define the statistics data with unique IDs
const statisticsData = [
  {
    id: "totalUsers",
    title: "Total Users",
    icon: <FaUsers className="text-blue-500" />,
  },
  {
    id: "premiumUsers",
    title: "Premium Users",
    icon: <MdPersonOutline className="text-green-500" />,
  },
  {
    id: "inactivePremiumUsers",
    title: "Inactive Premium Users",
    icon: <MdBlock className="text-yellow-500" />,
  },
  {
    id: "blockedUsers",
    title: "Blocked Users",
    icon: <FaRegHandPaper className="text-red-500" />,
  },
  {
    id: "activeUsers",
    title: "Active Users",
    icon: <FaUser className="text-green-500" />,
  },
  {
    id: "totalLanguages",
    title: "Total Languages",
    icon: <FaLanguage className="text-blue-500" />,
  },
  {
    id: "activeLanguages",
    title: "Active Languages",
    icon: <FaLanguage className="text-green-500" />,
  },
  {
    id: "inactiveLanguages",
    title: "Inactive Languages",
    icon: <MdBlock className="text-red-500" />,
  },
  {
    id: "totalCourses",
    title: "Total Courses",
    icon: <FaBookOpen className="text-blue-500" />,
  },
  {
    id: "activeCourses",
    title: "Active Courses",
    icon: <FaBook className="text-green-500" />,
  },
  {
    id: "inactiveCourses",
    title: "Inactive Courses",
    icon: <MdBlock className="text-red-500" />,
  },
  {
    id: "totalGroups",
    title: "Total Groups",
    icon: <FaLayerGroup className="text-blue-500" />,
  },
  {
    id: "activeGroups",
    title: "Active Groups",
    icon: <FaLayerGroup className="text-green-500" />,
  },
  {
    id: "inactiveGroups",
    title: "Inactive Groups",
    icon: <MdBlock className="text-red-500" />,
  },
  {
    id: "totalProposedCards",
    title: "Total Proposed Cards",
    icon: <FaRegHandPaper className="text-blue-500" />,
  },
  {
    id: "acceptedProposedCards",
    title: "Accepted Proposed Cards",
    icon: <FaCheck className="text-green-500" />,
  },
  {
    id: "rejectedProposedCards",
    title: "Rejected Proposed Cards",
    icon: <FaTimes className="text-red-500" />,
  },
  {
    id: "pendingProposedCards",
    title: "Pending Proposed Cards",
    icon: <FaClock className="text-yellow-500" />,
  },
  {
    id: "totalFlashcards",
    title: "Total Flashcards",
    icon: <MdFlashOn className="text-blue-500" />,
  },
  {
    id: "activeFlashcards",
    title: "Active Flashcards",
    icon: <MdFlashOn className="text-green-500" />,
  },
  {
    id: "inactiveFlashcards",
    title: "Inactive Flashcards",
    icon: <MdBlock className="text-red-500" />,
  },
];

const Analytics = (props) => {
  const [data, setData] = useState({});
  const API = useHttp();

  useEffect(() => {
    API.sendRequest(CONSTANTS.API.Dashboard.analytics, (res) => {
      setData(res?.data || {});
    });
  }, []);

  if (ROLES.DEFAULT === props?.profile?.role) {
    return (
      <Row
        gutter={[16, 16]}
        className="flex justify-center items-center h-full"
      >
        <Spin size="large" />
      </Row>
    );
  }

  // Function to render statistics based on category
  const renderStatistics = (category) =>
    statisticsData
      .filter((el) => el.title.toLowerCase().includes(category.toLowerCase()))
      .map((el) => (
        <Col span={8} xs={24} sm={12} md={8} lg={8} xl={8} key={el.id}>
          <Card
            size="small"
            className="p-5 hover:scale-105 transition-transform duration-300 ease-in-out"
          >
            <div className="flex items-center">
              <Title level={2} className="text-4xl mr-4 text-blue-500">
                {el.icon}
              </Title>
              <Statistic
                title={el.title}
                value={data[el.id] || 0}
                valueStyle={{ fontSize: "24px" }}
                className="text-xl font-bold text-gray-700 hover:text-blue-500 transition duration-300 ease-in-out"
              />
            </div>
          </Card>
        </Col>
      ));

  return (
    <Card className="mt-6">
      <Collapse defaultActiveKey={["1"]}>
        <Panel header="User Statistics" key="1">
          <Row gutter={[16, 16]}>{renderStatistics("User")}</Row>
        </Panel>
        <Panel header="Language Statistics" key="2">
          <Row gutter={[16, 16]}>{renderStatistics("Language")}</Row>
        </Panel>
        <Panel header="Course and Group Statistics" key="3">
          <Row gutter={[16, 16]}>
            {renderStatistics("Course")}
            {renderStatistics("Group")}
          </Row>
        </Panel>
        <Panel header="Proposed Cards and Flashcards" key="4">
          <Row gutter={[16, 16]}>{renderStatistics("Proposed")}</Row>
          <Row gutter={[16, 16]} className="mt-4">
            {renderStatistics("Flashcards")}
          </Row>
        </Panel>
      </Collapse>
    </Card>
  );
};

export default Analytics;

import { Breadcrumb, Row } from "antd";
import React, { useCallback } from "react";
import { useNavigate } from "react-router-dom";
import CRUDComponent from "../../../component/common/CRUD-Component";
import CONSTANTS from "../../../util/constant/CONSTANTS";
import { ROLES } from "../../../util/constant/dataConstants";

const QuizPage = (props) => {
  const navigate = useNavigate();
  const handleNavigate = useCallback(
    (id) => {
      // console.log(`Navigating to ${id}`);
      navigate(`${id}`);
    },
    [navigate]
  );
  return (
    <>
      <Breadcrumb
        className="mx-2 mb-3 mt-6"
        separator=" > "
        items={[
          {
            title: "Home",
          },
          {
            // href: `${appRoot}/${ROUTES.Quiz}`,
            title: "Quiz",
          },
        ]}
      />
      <Row>
        <CRUDComponent
          GET={{
            API: CONSTANTS.API.Quiz.getAll,
            DataModifier: (res, API, Setrefresh) => {
              return res?.map((el) => ({
                ...el,
                view: {
                  onClick: () => {
                    // if (el?.role === "user") {
                    //   return notification.info({
                    //     message: "This Section is not available to user",
                    //   });
                    // }
                    // navigate(`${el?.id}`);
                    handleNavigate(el?.id);
                  },
                  id: el?.id,
                },
              }));
            },
            column: CONSTANTS.TABLE.QUIZ,
          }}
          UPDATE={{
            API: CONSTANTS.API.Quiz.update,
            message: "Updated Quiz successfully",
            modaltitle: "Update Quiz",
            modalFields: CONSTANTS.FORM_FIELD.EDIT_QUIZ,
            // payloadModifier: (res) => res,
          }}
          CREATE={
            [
              ROLES.ADMIN,
              ROLES.DATA,
              // ROLES.IMAGE,
              // ROLES.INTERN,
              // ROLES.SEO,
              ROLES.SUPER_ADMIN,
              // ROLES.SUPERUSER,
              // ROLES.WRITER,
            ].some((el) => el === props?.profile?.role) && {
              name: "Add a Quiz",
              API: CONSTANTS.API.Quiz.create,
              message: "Created Quiz successfully",
              modaltitle: "Add Quiz",
              modalFields: CONSTANTS.FORM_FIELD.QUIZ,
            }
          }
          DELETE={{
            API: CONSTANTS.API.Quiz.delete,
            message: "Deleted Successfully",
            confirm_message: "Are you sure to Delete this Quiz?",
          }}
          // FILTERSORTKEY={{
          //   cityName: "$city.name$",
          // }}
          datefilter
          isSearch
          selectionOff
          props={props}
        />
      </Row>
    </>
  );
};

export default QuizPage;

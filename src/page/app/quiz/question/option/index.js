import { Breadcrumb, <PERSON> } from "antd";
import React, { useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import CRUDComponent from "../../../../../component/common/CRUD-Component";
import CONSTANTS from "../../../../../util/constant/CONSTANTS";
import { ROLES } from "../../../../../util/constant/dataConstants";
import { appRoot, ROUTES } from "../../../../../util/Route";

const OptionPage = (props) => {
  const paramas = useParams();
  // const [extraQuery]= useState({})
  const [extraQuery]= useState({questionId :paramas?.questionId})

  const navigate = useNavigate();

  const handleBreadcrumbClick = (route) => {
    navigate(route);
  };

  return (
    <>
      <Breadcrumb
        className="mx-2 mb-3 mt-6"
        separator=" > "
        items={[
          {
            title: "Home",
          },
          {
            onClick: () => handleBreadcrumbClick(`${appRoot}/${ROUTES.QUIZ}`),
            title: "Quiz",
          },
          {
            onClick: () => handleBreadcrumbClick(`${appRoot}/${ROUTES.QUIZ}/${paramas?.quizId}`),
            title: "Question",
          },
          {
            title: "Option",
          },
        ]}
        params={{

        }}
      />
      <Row>
        <CRUDComponent
          GET={{
            // extraQuery: {
            //   questionId: paramas?.questionId,
            // },
            extraQuery,
            API: CONSTANTS.API.QuestionOption.getAll,
            DataModifier: (res, API, Setrefresh) => {
              return res?.map((el) => ({
                ...el,
                // no: `${el?.id}`.padStart(6, "0"),
              
                status: {
                  checked: el?.isActive,
                  id: el.id,
                  onClick: (id, val) => {
                    const UpdateAPIEnd = {
                      ...CONSTANTS?.API.QuestionOption.update,
                    };
                    UpdateAPIEnd.endpoint = `${UpdateAPIEnd?.endpoint}${id}`;
                    API.sendRequest(
                      UpdateAPIEnd,
                      () => {
                        Setrefresh((prev) => !prev);
                      },
                      { isActive: !val },
                      `QuestionOption ${
                        !val ? "activated" : "deactivated"
                      } Successfully`,
                      () => {},
                      "Currently Not working We will update Soon"
                    );
                  },
                },
              }));
            },
            column: CONSTANTS.TABLE.OPTION,
          }}
          UPDATE={{
            API: CONSTANTS.API.QuestionOption.update,
            message: "Updated QuestionOption successfully",
            modaltitle: "Update QuestionOption",
            modalFields: CONSTANTS.FORM_FIELD.OPTION,
            // payloadModifier: (res) => {
            //     console.log(res)
            //     return res
            // },
          }}
          CREATE={
            [
              ROLES.ADMIN,
              ROLES.DATA,
              // ROLES.IMAGE,
              // ROLES.INTERN,
              // ROLES.SEO,
              ROLES.SUPER_ADMIN,
              // ROLES.SUPERUSER,
              // ROLES.WRITER,
            ].some((el) => el === props?.profile?.role) && {
              name: "Add a QuestionOption",
              API: CONSTANTS.API.QuestionOption.create,
              message: "Created QuestionOption successfully",
              modaltitle: "Add QuestionOption",
              modalFields: CONSTANTS.FORM_FIELD.OPTION,
              payloadModifier: (res) => {
                // console.log(res)
                const payload = {...res , questionId :paramas?.questionId}
                return payload
            },
            // AddAfterAddButton: (
            //     <>
            //     <FlashCardModalwithId questionId={paramas?.questionId} />
            //     </>
            //   ),
            // }
        }
          }
          DELETE={{
            API: CONSTANTS.API.QuestionOption.delete,
            message: "Deleted Successfully",
            confirm_message: "Are you sure to Delete this category?",
          }}
          // FILTERSORTKEY={{
          //   cityName: "$city.name$",
          // }}
          datefilter
          isSearch
          selectionOff
          props={props}
        />
      </Row>
    </>
  );
};

export default OptionPage;

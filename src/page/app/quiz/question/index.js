import { Breadcrumb, Row } from "antd";
import React, { useCallback, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import CRUDComponent from "../../../../component/common/CRUD-Component";
import CONSTANTS from "../../../../util/constant/CONSTANTS";
import { ROLES } from "../../../../util/constant/dataConstants";
import { appRoot, ROUTES } from "../../../../util/Route";

const QuestionPage = (props) => {
  const paramas = useParams();
  const [extraQuery]= useState({...paramas})

  const navigate = useNavigate();

  const handleBreadcrumbClick = (route) => {
    navigate(route);
  };
//   const navigate = useNavigate();
  const handleNavigate = useCallback(
    (id) => {
      // console.log(`Navigating to ${id}`);
      navigate(`${id}`);
    },
    [navigate]
  );
  return (
    <>
      <Breadcrumb
        className="mx-2 mb-3 mt-6"
        separator=" > "
        items={[
          {
            title: "Home",
          },
          {
            onClick: () => handleBreadcrumbClick(`${appRoot}/${ROUTES.QUIZ}`),
            title: "Quiz",
          },
          {
            title: "Question",
          },
        ]}
        params={{

        }}
      />
      <Row>
        <CRUDComponent
          GET={{
            // extraQuery: {
            //   quizId: paramas?.quizId,
            // },
            extraQuery,
            API: CONSTANTS.API.Question.getAll,
            DataModifier: (res, API, Setrefresh) => {
              return res?.map((el) => ({
                ...el,
                // no: `${el?.id}`.padStart(6, "0"),
                view: {
                    onClick: () => {
                      // if (el?.role === "user") {
                      //   return notification.info({
                      //     message: "This Section is not available to user",
                      //   });
                      // }
                      // navigate(`${el?.id}`);
                      handleNavigate(el?.id);
                    },
                    id: el?.id,
                  },
                status: {
                  checked: el?.isActive,
                  id: el.id,
                  onClick: (id, val) => {
                    const UpdateAPIEnd = {
                      ...CONSTANTS?.API.Question.update,
                    };
                    UpdateAPIEnd.endpoint = `${UpdateAPIEnd?.endpoint}${id}`;
                    API.sendRequest(
                      UpdateAPIEnd,
                      () => {
                        Setrefresh((prev) => !prev);
                      },
                      { isActive: !val },
                      `Question ${
                        !val ? "activated" : "deactivated"
                      } Successfully`,
                      () => {},
                      "Currently Not working We will update Soon"
                    );
                  },
                },
              }));
            },
            column: CONSTANTS.TABLE.QUESTION,
          }}
          UPDATE={{
            API: CONSTANTS.API.Question.update,
            message: "Updated Question successfully",
            modaltitle: "Update Question",
            modalFields: CONSTANTS.FORM_FIELD.EDIT_QUESTION,
            // payloadModifier: (res) => {
            //     console.log(res)
            //     return res
            // },
          }}
          CREATE={
            [
              ROLES.ADMIN,
              ROLES.DATA,
              // ROLES.IMAGE,
              // ROLES.INTERN,
              // ROLES.SEO,
              ROLES.SUPER_ADMIN,
              // ROLES.SUPERUSER,
              // ROLES.WRITER,
            ].some((el) => el === props?.profile?.role) && {
              name: "Add a Question",
              API: CONSTANTS.API.Question.create,
              message: "Created Question successfully",
              modaltitle: "Add Question",
              modalFields: CONSTANTS.FORM_FIELD.QUESTION,
              payloadModifier: (res) => {
                // console.log(res)
                const payload = {...res , quizId :paramas?.quizId}
                return payload
            },
            // AddAfterAddButton: (
            //     <>
            //     <FlashCardModalwithId quizId={paramas?.quizId} />
            //     </>
            //   ),
            // }
        }
          }
          DELETE={{
            API: CONSTANTS.API.Question.delete,
            message: "Deleted Successfully",
            confirm_message: "Are you sure to Delete this Question?",
          }}
          // FILTERSORTKEY={{
          //   cityName: "$city.name$",
          // }}
          datefilter
          isSearch
          selectionOff
          props={props}
        />
      </Row>
    </>
  );
};

export default QuestionPage;

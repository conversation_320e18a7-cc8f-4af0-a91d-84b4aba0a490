import { Breadcrumb, <PERSON> } from "antd";
import React from "react";
import CRUDComponent from "../../../component/common/CRUD-Component";
import CONSTANTS from "../../../util/constant/CONSTANTS";
import { ROLES } from "../../../util/constant/dataConstants";

const LevelPage = (props) => {
  return (
    <>
      <Breadcrumb
        className="mx-2 mb-3 mt-6"
        separator=" > "
        items={[
          {
            title: "Home",
          },
          {
            // href: `${appRoot}/${ROUTES.Level}`,
            title: "Level",
          },
        ]}
      />
      <Row>
        <CRUDComponent
          GET={{
            API: CONSTANTS.API.Level.getAll,
            DataModifier: (res, API, Setrefresh) => {
              return res?.map((el) => ({
                ...el,
              }));
            },
            column: CONSTANTS.TABLE.LEVEL,
          }}
          UPDATE={{
            API: CONSTANTS.API.Level.update,
            message: "Updated Level successfully",
            modaltitle: "Update Level",
            modalFields: CONSTANTS.FORM_FIELD.LEVEL,
            // payloadModifier: (res) => res,
          }}
          CREATE={
            [
              ROLES.ADMIN,
              ROLES.DATA,
              // ROLES.IMAGE,
              // ROLES.INTERN,
              // ROLES.SEO,
              ROLES.SUPER_ADMIN,
              // ROLES.SUPERUSER,
              // ROLES.WRITER,
            ].some((el) => el === props?.profile?.role) && {
              name: "Add a Level",
              API: CONSTANTS.API.Level.create,
              message: "Created Level successfully",
              modaltitle: "Add Level",
              modalFields: CONSTANTS.FORM_FIELD.LEVEL,
            }
          }
          DELETE={{
            API: CONSTANTS.API.Level.delete,
            message: "Deleted Successfully",
            confirm_message: "Are you sure to Delete this Level?",
          }}
          // FILTERSORTKEY={{
          //   cityName: "$city.name$",
          // }}
          datefilter
          isSearch
          selectionOff
          props={props}
        />
      </Row>
    </>
  );
};

export default LevelPage;

import { <PERSON>readcrum<PERSON>, <PERSON> } from "antd";
import React, { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import FlashCardSearchandAdd from "../../../../component/common/card/FlashCardSearchandAdd";
import CRUDComponent from "../../../../component/common/CRUD-Component";
import useHttp from "../../../../hooks/use-http";
import CONSTANTS from "../../../../util/constant/CONSTANTS";
import { appRoot, ROUTES } from "../../../../util/Route";

const SingleCoursePage = (props) => {
  const paramas = useParams();
  const [extraQuery, setExtraQuery] = useState({ ...paramas });
  const [flashcardData, setFlshcardData] = useState({});

const API = useHttp()

  useEffect(()=>{
    const GetOneCourseAPIEnd = {...CONSTANTS.API.Course.getOne}
    GetOneCourseAPIEnd.endpoint = `${GetOneCourseAPIEnd?.endpoint}${paramas?.courseId}`;

API.sendRequest(GetOneCourseAPIEnd,(res)=>{
  setFlshcardData(res?.data);

})
},[])
  const navigate = useNavigate();

  const handleBreadcrumbClick = (route) => {
    navigate(route);
  };

  return (
    <>
      <Breadcrumb
        className="mx-2 mb-3 mt-6"
        separator=" > "
        items={[
          {
            title: "Home",
          },
          {
            onClick: () => handleBreadcrumbClick(`${appRoot}/${ROUTES.COURSE}`),
            title: "Course",
          },
          {
            title: "CourseFlashcard",
          },
        ]}
        params={{}}
      />
      <Row>
        <CRUDComponent
          GET={{
            // extraQuery: {
            //   courseId: paramas?.courseId,
            // },
            extraQuery,
            API: CONSTANTS.API.CourseFlashcard.getAll,
            DataModifier: (res, API, Setrefresh) => {
              return res?.map((el) => ({
                ...el?.flashcard,
                ...el,
                // status: {
                //   checked: el?.isActive,
                //   id: el.id,
                //   onClick: (id, val) => {
                //     const UpdateAPIEnd = {
                //       ...CONSTANTS?.API.CourseFlashcard.update,
                //     };
                //     UpdateAPIEnd.endpoint = `${UpdateAPIEnd?.endpoint}${id}`;
                //     API.sendRequest(
                //       UpdateAPIEnd,
                //       () => {
                //         Setrefresh((prev) => !prev);
                //       },
                //       { isActive: !val },
                //       `CourseFlashcard ${
                //         !val ? "activated" : "deactivated"
                //       } Successfully`,
                //       () => {},
                //       "Currently Not working We will update Soon"
                //     );
                //   },
                // },
              }));
            },
            column: CONSTANTS.TABLE.GROUP_FLASHCARD,
          }}
          // UPDATE={{
          //   API: CONSTANTS.API.CourseFlashcard.update,
          //   message: "Updated CourseFlashcard successfully",
          //   modaltitle: "Update CourseFlashcard",
          //   modalFields: CONSTANTS.FORM_FIELD.GROUP_FLASHCARD,
          //   // payloadModifier: (res) => {
          //   //     console.log(res)
          //   //     return res
          //   // },
          // }}
          // CREATE={
          //   [
          //     ROLES.ADMIN,
          //     ROLES.DATA,
          //     // ROLES.IMAGE,
          //     // ROLES.INTERN,
          //     // ROLES.SEO,
          //     ROLES.SUPER_ADMIN,
          //     // ROLES.SUPERUSER,
          //     // ROLES.WRITER,
          //   ].some((el) => el === props?.profile?.role) && {
          //     name: "Add a CourseFlashcard",
          //     API: CONSTANTS.API.CourseFlashcard.create,
          //     message: "Created CourseFlashcard successfully",
          //     modaltitle: "Add CourseFlashcard",
          //     modalFields: CONSTANTS.FORM_FIELD.GROUP_FLASHCARD,
          //     payloadModifier: (res) => {
          //       console.log(res)
          //       const payload = {...res , courseId :paramas?.courseId}
          //       return payload
          //   },
          //   // AddAfterAddButton: (
          //   //     <>
          //   //     <FlashCardModalwithId courseId={paramas?.courseId} />
          //   //     </>
          //   //   ),
          //   }
          // }
          DELETE={{
            API: CONSTANTS.API.CourseFlashcard.delete,
            message: "Deleted Successfully",
            confirm_message: "Are you sure to Delete this CourseFlashcard?",
          }}
          // FILTERSORTKEY={{
          //   cityName: "$city.name$",
          // }}
          datefilter
          // isSearch
          selectionOff
          props={props}
          AddOnComponent={
            <>
              <FlashCardSearchandAdd
              query={{languageId : flashcardData?.languageId}}
                optionmodifier={(option) => {
                  return {
                    id: option?.id,
                    name: `${option?.word} - ${option?.pronunciation}`,
                    description: `${option?.example}`,
                  };
                }}
                payloadmodifier={(pay) => {
                  return {
                    courseId: paramas?.courseId,
                    flashcardId: pay?.id,
                    position: 100,
                  };
                }}
                endpointObject={CONSTANTS.API.CourseFlashcard.create}
                refresher={() => {
                  setExtraQuery((prev) => ({ ...prev }));
                }}
              />
            </>
          }
        />
      </Row>
    </>
  );
};

export default SingleCoursePage;

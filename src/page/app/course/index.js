import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON> } from "antd";
import React, { useCallback, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import CRUDComponent from "../../../component/common/CRUD-Component";
import useHttp from "../../../hooks/use-http";
import CONSTANTS from "../../../util/constant/CONSTANTS";
import { ROLES } from "../../../util/constant/dataConstants";

const CoursePage = (props) => {
  const API = useHttp();
  useEffect(() => {
    API.sendRequest(
      CONSTANTS.API.Language.getAll,
      (res) => {
        const LangugesArr = res?.data?.rows?.map((el) => ({
          ...el,
          label: ` ${el?.name} ( ${el?.code} - ${el?.englishName})`,
          // label: `${el?.code} - ${el?.name} ( ${el?.englishName})`,
          value: el?.id,
        }));
        CONSTANTS.FORM_FIELD.FLASHCARD.find(
          (el) => el?.id === "languageId"
        ).option = LangugesArr;
        CONSTANTS.FORM_FIELD.GROUP.find(
          (el) => el?.id === "languageId"
        ).option = LangugesArr;
        CONSTANTS.FORM_FIELD.COURSE.find(
          (el) => el?.id === "languageId"
        ).option = LangugesArr;
      },
      { isActive: true,  }
    );
    API.sendRequest(
      CONSTANTS.API.Level.getAll,
      (res) => {
        const LevelsArr = res?.data?.rows?.map((el) => ({
          ...el,
          label: `${el?.name}`,
          // label: ` ${el?.name} ( ${el?.code} - ${el?.englishName})`,
          // label: `${el?.code} - ${el?.name} ( ${el?.englishName})`,
          value: el?.id,
        }));
        // console.log(LevelsArr);
        // CONSTANTS.FORM_FIELD.FLASHCARD.find(
        //   (el) => el?.id === "levelId"
        // ).option = LevelsArr;
        // CONSTANTS.FORM_FIELD.GROUP.find(
        //   (el) => el?.id === "levelId"
        // ).option = LevelsArr;
        CONSTANTS.FORM_FIELD.COURSE.find((el) => el?.id === "levelId").option =
          LevelsArr;
      }
      // { isActive: true, isPremium: true }
    );
  }, []);
  const navigate = useNavigate();
  const handleNavigate = useCallback(
    (id) => {
      // console.log(`Navigating to ${id}`);
      navigate(`${id}`);
    },
    [navigate]
  );
  return (
    <>
      <Breadcrumb
        className="mx-2 mb-3 mt-6"
        separator=" > "
        items={[
          {
            title: "Home",
          },
          {
            // href: `${appRoot}/${ROUTES.Course}`,
            title: "Course",
          },
        ]}
      />
      <Row>
        <CRUDComponent
          GET={{
            API: CONSTANTS.API.Course.getAll,
            DataModifier: (res, API, Setrefresh) => {
              return res?.map((el) => ({
                ...el,
                // no: `${el?.id}`.padStart(6, "0"),
                view: {
                  onClick: () => {
                    // if (el?.role === "user") {
                    //   return notification.info({
                    //     message: "This Section is not available to user",
                    //   });
                    // }
                    // navigate(`${el?.id}`);
                    handleNavigate(el?.id);
                  },
                  id: el?.id,
                },
                status: {
                  checked: el?.isActive,
                  id: el.id,
                  onClick: (id, val) => {
                    const UpdateAPIEnd = { ...CONSTANTS?.API.Course.update };
                    UpdateAPIEnd.endpoint = `${UpdateAPIEnd?.endpoint}${id}`;
                    API.sendRequest(
                      UpdateAPIEnd,
                      () => {
                        Setrefresh((prev) => !prev);
                      },
                      { isActive: !val },
                      `Course ${
                        !val ? "activated" : "deactivated"
                      } Successfully`,
                      () => {},
                      "Currently Not working We will update Soon"
                    );
                  },
                },
                premium: {
                  checked: el?.isPremium,
                  id: el.id,
                  onClick: (id, val) => {
                    const UpdateAPIEnd = { ...CONSTANTS?.API.Course.update };
                    UpdateAPIEnd.endpoint = `${UpdateAPIEnd?.endpoint}${id}`;
                    API.sendRequest(
                      UpdateAPIEnd,
                      () => {
                        Setrefresh((prev) => !prev);
                      },
                      { isPremium: !val },
                      `Course ${
                        !val ? "primiumed" : "de-premiumed"
                      } Successfully`,
                      () => {},
                      "Currently Not working We will update Soon"
                    );
                  },
                },
              }));
            },
            column: CONSTANTS.TABLE.COURSE,
          }}
          UPDATE={{
            API: CONSTANTS.API.Course.update,
            message: "Updated Course successfully",
            modaltitle: "Update Course",
            modalFields: CONSTANTS.FORM_FIELD.COURSE,
            isFormData: true,
            // payloadModifier: (res) => res,
          }}
          CREATE={
            [
              ROLES.ADMIN,
              ROLES.DATA,
              // ROLES.IMAGE,
              // ROLES.INTERN,
              // ROLES.SEO,
              ROLES.SUPER_ADMIN,
              // ROLES.SUPERUSER,
              // ROLES.WRITER,
            ].some((el) => el === props?.profile?.role) && {
              name: "Add a Course",
              API: CONSTANTS.API.Course.create,
              message: "Created Course successfully",
              modaltitle: "Add Course",
              modalFields: CONSTANTS.FORM_FIELD.COURSE,
              isFormData: true,
            }
          }
          DELETE={{
            API: CONSTANTS.API.Course.delete,
            message: "Deleted Successfully",
            confirm_message: "Are you sure to Delete this Course?",
          }}
          // FILTERSORTKEY={{
          //   cityName: "$city.name$",
          // }}
          datefilter
          isSearch
          selectionOff
          props={props}
        />
      </Row>
    </>
  );
};

export default CoursePage;

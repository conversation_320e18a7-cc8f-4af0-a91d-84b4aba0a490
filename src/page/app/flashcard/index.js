import { <PERSON><PERSON><PERSON>rum<PERSON>, Row } from "antd";
import React, { useCallback, useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import BulkFlashcardViaImage from "../../../component/common/advance/BulkFlashcardViaImage";
import CRUDComponent from "../../../component/common/CRUD-Component";
import useHttp from "../../../hooks/use-http";
import CONSTANTS from "../../../util/constant/CONSTANTS";
import { ROLES } from "../../../util/constant/dataConstants";

const FlashcardPage = (props) => {
  const API = useHttp();
  const [extraQuery, setExtraQuery] = useState({});
  const navigate = useNavigate();
  useEffect(() => {
    API.sendRequest(
      CONSTANTS.API.Language.getAll,
      (res) => {
        const LangugesArr = res?.data?.rows?.map((el) => ({
          ...el,
          label: ` ${el?.name} ( ${el?.code} - ${el?.englishName})`,
          // label: `${el?.code} - ${el?.name} ( ${el?.englishName})`,
          value: el?.id,
          id: el?.id,
          text: ` ${el?.name} ( ${el?.code} - ${el?.englishName})`,
        }));
        CONSTANTS.FORM_FIELD.FLASHCARD.find(
          (el) => el?.id === "languageId"
        ).option = LangugesArr;
        CONSTANTS.FORM_FIELD.FLASHCCARD_BULK_IMAGE.find(
          (el) => el?.id === "languageId"
        ).option = LangugesArr;
        CONSTANTS.FORM_FIELD.GROUP.find(
          (el) => el?.id === "languageId"
        ).option = LangugesArr;
        CONSTANTS.FORM_FIELD.COURSE.find(
          (el) => el?.id === "languageId"
        ).option = LangugesArr;
        CONSTANTS.TABLE.FLASHCARD.find(
          (el) => el?.dataIndex === "language"
        ).filters = LangugesArr;
      },
      { isActive: true,  }
    );
  }, []);
  const handleNavigate = useCallback(
    (id) => {
      // console.log(`Navigating to ${id}`);
      navigate(`${id}`);
    },
    [navigate]
  );
  return (
    <>
      <Breadcrumb
        className="mx-2 mb-3 mt-6"
        separator=" > "
        items={[
          {
            title: "Home",
          },
          {
            // href: `${appRoot}/${ROUTES.Flashcard}`,
            title: "Flashcard",
          },
        ]}
      />
      <Row>
        <CRUDComponent
          GET={{
            extraQuery,
            API: CONSTANTS.API.Flashcard.getAll,
            DataModifier: (res, API, Setrefresh) => {
              return res?.map((el) => ({
                ...el,
                // no: `${el?.id}`.padStart(6, "0"),
                view: {
                  onClick: () => {
                    // if (el?.role === "user") {
                    //   return notification.info({
                    //     message: "This Section is not available to user",
                    //   });
                    // }
                    // navigate(`${el?.id}`);
                    handleNavigate(el?.id);
                  },
                  id: el?.id,
                },
                like: {
                  onClick: () => {
                    const UpdateAPIEnd = {
                      ...CONSTANTS?.API.Flashcard.likedislike,
                    };
                    UpdateAPIEnd.endpoint = `${UpdateAPIEnd?.endpoint}${el?.id}`;
                    API.sendRequest(
                      UpdateAPIEnd,
                      () => {
                        Setrefresh((prev) => !prev);
                      },
                      null,
                      "liek dislike toggeled for user",

                      () => {},
                      "Currently Not working We will update Soon"
                    );
                  },
                  id: el?.id,
                },
                bookmark: {
                  onClick: () => {
                    const UpdateAPIEnd = {
                      ...CONSTANTS?.API.Flashcard.bookmark,
                    };
                    UpdateAPIEnd.endpoint = `${UpdateAPIEnd?.endpoint}${el?.id}`;
                    API.sendRequest(
                      UpdateAPIEnd,
                      () => {
                        Setrefresh((prev) => !prev);
                      },
                      null,
                      "bookmarktoggeled for user",

                      () => {},
                      "Currently Not working We will update Soon"
                    );
                  },
                  id: el?.id,
                },

                status: {
                  checked: el?.isActive,
                  id: el.id,
                  onClick: (id, val) => {
                    const UpdateAPIEnd = { ...CONSTANTS?.API.Flashcard.update };
                    UpdateAPIEnd.endpoint = `${UpdateAPIEnd?.endpoint}${id}`;
                    API.sendRequest(
                      UpdateAPIEnd,
                      () => {
                        Setrefresh((prev) => !prev);
                      },
                      { isActive: !val },
                      `Flashcard ${
                        !val ? "activated" : "deactivated"
                      } Successfully`,
                      () => {},
                      "Currently Not working We will update Soon"
                    );
                  },
                },
              }));
            },
            column: CONSTANTS.TABLE.FLASHCARD,
          }}
          UPDATE={{
            API: CONSTANTS.API.Flashcard.update,
            message: "Updated Flashcard successfully",
            modaltitle: "Update Flashcard",
            modalFields: CONSTANTS.FORM_FIELD.FLASHCARD,
            isFormData: true,
            // payloadModifier: (res) => res,
          }}
          CREATE={
            [
              ROLES.ADMIN,
              ROLES.DATA,
              // ROLES.IMAGE,
              // ROLES.INTERN,
              // ROLES.SEO,
              ROLES.SUPER_ADMIN,
              // ROLES.SUPERUSER,
              // ROLES.WRITER,
            ].some((el) => el === props?.profile?.role) && {
              name: "Add a Flashcard",
              API: CONSTANTS.API.Flashcard.create,
              message: "Created Flashcard successfully",
              modaltitle: "Add Flashcard",
              modalFields: CONSTANTS.FORM_FIELD.FLASHCARD,
              isFormData: true,
              AddAfterAddButton: (
                <>
                  <BulkFlashcardViaImage
                    refresher={() => {
                      setExtraQuery((prev) => ({ ...prev }));
                    }}
                  />
                </>
              ),
            }
          }
          DELETE={{
            API: CONSTANTS.API.Flashcard.delete,
            message: "Deleted Successfully",
            confirm_message: "Are you sure to Delete this category?",
          }}
          FILTERSORTKEY={{
            language: "languageId",
            // status: "isActive",
          }}
          datefilter
          isSearch
          selectionOff
          props={props}
        />
      </Row>
    </>
  );
};

export default FlashcardPage;

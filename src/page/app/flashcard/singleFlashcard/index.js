import { Breadcrumb, Row } from "antd";
import React, { useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import FlashCardModalwithId from "../../../../component/common/card/FlashCardModalwithId";
import CRUDComponent from "../../../../component/common/CRUD-Component";
import CONSTANTS from "../../../../util/constant/CONSTANTS";
import { ROLES } from "../../../../util/constant/dataConstants";
import { appRoot, ROUTES } from "../../../../util/Route";

const SingleFlashcardPage = (props) => {
  const paramas = useParams();
  const [extraQuery]= useState({...paramas})

  const navigate = useNavigate();

  const handleBreadcrumbClick = (route) => {
    navigate(route);
  };

  return (
    <>
      <Breadcrumb
        className="mx-2 mb-3 mt-6"
        separator=" > "
        items={[
          {
            title: "Home",
          },
          {
            onClick: () => handleBreadcrumbClick(`${appRoot}/${ROUTES.FLASHCARD}`),
            title: "Flashcards",
          },
          {
            title: "SimilarWords",
          },
        ]}
        params={{

        }}
      />
      <Row>
        <CRUDComponent
          GET={{
            // extraQuery: {
            //   flashcardId: paramas?.flashcardId,
            // },
            extraQuery,
            API: CONSTANTS.API.SimilarWords.getAll,
            DataModifier: (res, API, Setrefresh) => {
              return res?.map((el) => ({
                ...el,
                // no: `${el?.id}`.padStart(6, "0"),
                status: {
                  checked: el?.isActive,
                  id: el.id,
                  onClick: (id, val) => {
                    const UpdateAPIEnd = {
                      ...CONSTANTS?.API.SimilarWords.update,
                    };
                    UpdateAPIEnd.endpoint = `${UpdateAPIEnd?.endpoint}${id}`;
                    API.sendRequest(
                      UpdateAPIEnd,
                      () => {
                        Setrefresh((prev) => !prev);
                      },
                      { isActive: !val },
                      `SimilarWords ${
                        !val ? "activated" : "deactivated"
                      } Successfully`,
                      () => {},
                      "Currently Not working We will update Soon"
                    );
                  },
                },
              }));
            },
            column: CONSTANTS.TABLE.SIMILAR_WORDS,
          }}
          UPDATE={{
            API: CONSTANTS.API.SimilarWords.update,
            message: "Updated SimilarWords successfully",
            modaltitle: "Update SimilarWords",
            modalFields: CONSTANTS.FORM_FIELD.SIMILAR_WORDS,
            // payloadModifier: (res) => {
            //     console.log(res)
            //     return res
            // },
          }}
          CREATE={
            [
              ROLES.ADMIN,
              ROLES.DATA,
              // ROLES.IMAGE,
              // ROLES.INTERN,
              // ROLES.SEO,
              ROLES.SUPER_ADMIN,
              // ROLES.SUPERUSER,
              // ROLES.WRITER,
            ].some((el) => el === props?.profile?.role) && {
              name: "Add a SimilarWords",
              API: CONSTANTS.API.SimilarWords.create,
              message: "Created SimilarWords successfully",
              modaltitle: "Add SimilarWords",
              modalFields: CONSTANTS.FORM_FIELD.SIMILAR_WORDS,
              payloadModifier: (res) => {
                // console.log(res)
                const payload = {...res , flashcardId :paramas?.flashcardId}
                return payload
            },
            AddAfterAddButton: (
                <>
                <FlashCardModalwithId flashcardId={paramas?.flashcardId} />
                </>
              ),
            }
          }
          DELETE={{
            API: CONSTANTS.API.SimilarWords.delete,
            message: "Deleted Successfully",
            confirm_message: "Are you sure to Delete this category?",
          }}
          // FILTERSORTKEY={{
          //   cityName: "$city.name$",
          // }}
          datefilter
          isSearch
          selectionOff
          props={props}
        />
      </Row>
    </>
  );
};

export default SingleFlashcardPage;

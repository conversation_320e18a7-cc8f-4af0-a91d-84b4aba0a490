import { Breadcrumb, Row } from "antd";
import React from "react";
import CRUDComponent from "../../../component/common/CRUD-Component";
import CONSTANTS from "../../../util/constant/CONSTANTS";
import { ROLES } from "../../../util/constant/dataConstants";

const LanguagePage = (props) => {
  return (
    <>
      <Breadcrumb
        className="mx-2 mb-3 mt-6"
        separator=" > "
        items={[
          {
            title: "Home",
          },
          {
            // href: `${appRoot}/${ROUTES.Language}`,
            title: "Language",
          },
        ]}
      />
      <Row>
        <CRUDComponent
          GET={{
            API: CONSTANTS.API.Language.getAll,
            DataModifier: (res, API, Setrefresh) => {
              return res?.map((el) => ({
                ...el,
                // no: `${el?.id}`.padStart(6, "0"),
                status: {
                  checked: el?.isActive,
                  id: el.id,
                  onClick: (id, val) => {
                    const UpdateAPIEnd = { ...CONSTANTS?.API.Language.update };
                    UpdateAPIEnd.endpoint = `${UpdateAPIEnd?.endpoint}${id}`;
                    API.sendRequest(
                      UpdateAPIEnd,
                      () => {
                        Setrefresh((prev) => !prev);
                      },
                      { isActive: !val },
                      `Language ${
                        !val ? "activated" : "deactivated"
                      } Successfully`,
                      () => {},
                      "Currently Not working We will update Soon"
                    );
                  },
                },
                premium: {
                  checked: el?.isPremium,
                  id: el.id,
                  onClick: (id, val) => {
                    const UpdateAPIEnd = { ...CONSTANTS?.API.Language.update };
                    UpdateAPIEnd.endpoint = `${UpdateAPIEnd?.endpoint}${id}`;
                    API.sendRequest(
                      UpdateAPIEnd,
                      () => {
                        Setrefresh((prev) => !prev);
                      },
                      { isPremium: !val },
                      `Language ${
                        !val ? "primiumed" : "de-premiumed"
                      } Successfully`,
                      () => {},
                      "Currently Not working We will update Soon"
                    );
                  },
                },
              }));
            },
            column: CONSTANTS.TABLE.LANGUAGE,
          }}
          UPDATE={{
            API: CONSTANTS.API.Language.update,
            message: "Updated Language successfully",
            modaltitle: "Update Language",
            modalFields: CONSTANTS.FORM_FIELD.LANGUAGE,
            // payloadModifier: (res) => res,
          }}
          CREATE={
            [
              ROLES.ADMIN,
              ROLES.DATA,
              // ROLES.IMAGE,
              // ROLES.INTERN,
              // ROLES.SEO,
              ROLES.SUPER_ADMIN,
              // ROLES.SUPERUSER,
              // ROLES.WRITER,
            ].some((el) => el === props?.profile?.role) && {
              name: "Add a Language",
              API: CONSTANTS.API.Language.create,
              message: "Created Language successfully",
              modaltitle: "Add Language",
              modalFields: CONSTANTS.FORM_FIELD.LANGUAGE,
            }
          }
          DELETE={{
            API: CONSTANTS.API.Language.delete,
            message: "Deleted Successfully",
            confirm_message: "Are you sure to Delete this Language?",
          }}
          FILTERSORTKEY={{
            // language: "languageId",
            status: "isActive",
            premium : "isPremium"
          }}
          datefilter
          isSearch
          selectionOff
          props={props}
        />
      </Row>
    </>
  );
};

export default LanguagePage;

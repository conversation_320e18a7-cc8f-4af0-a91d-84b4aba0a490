import React, { useEffect, useState } from "react";
import {
  Card,
  Row,
  Col,
  Statistic,
  Spin,
  Button,
  DatePicker,
  Space,
  Typography,
} from "antd";
import { ReloadOutlined } from "@ant-design/icons";
import { Pie, Column, Line } from "@ant-design/plots";
import CONSTANTS from "../../../util/constant/CONSTANTS";
import useHttp from "../../../hooks/use-http";
import dayjs from "dayjs";

const { RangePicker } = DatePicker;
const { Title } = Typography;

const AnalyticsChart = () => {
  const [analyticsData, setAnalyticsData] = useState(null);
  //   const [loading, setLoading] = useState(true);
  const [refresh, setRefresh] = useState(false);
  const [dateRange, setDateRange] = useState([
    dayjs().subtract(30, "days"),
    dayjs(),
  ]);
  const API = useHttp();

  const fetchAnalytics = () => {
    // setLoading(true);
    const params = {
      startDate: dateRange[0].format("YYYY-MM-DD"),
      endDate: dateRange[1].format("YYYY-MM-DD"),
    };

    API.sendRequest(
      CONSTANTS.API.Logs.getAnalytics,
      (res) => {
        setAnalyticsData(res.data);
        // setLoading(false);
      },
      params
    );
  };

  useEffect(() => {
    fetchAnalytics();
  }, [refresh]);

  const handleDateRangeChange = (dates) => {
    if (dates) {
      setDateRange(dates);
      setRefresh((prev) => !prev);
    }
  };

  const handleRefresh = () => {
    setRefresh((prev) => !prev);
  };

  if (API.isLoading && !analyticsData) {
    return (
      <div style={{ textAlign: "center", padding: "50px" }}>
        <Spin size="large" />
        <p>Loading analytics data...</p>
      </div>
    );
  }

  if (!analyticsData) {
    return (
      <div style={{ textAlign: "center", padding: "50px" }}>
        <p>No analytics data available</p>
        <Button type="primary" onClick={handleRefresh}>
          Refresh
        </Button>
      </div>
    );
  }

  const { statusCodeData, methodData, durationData, timeSeriesData, stats } =
    analyticsData;

  // Prepare data for charts
  const statusCodeChartData = statusCodeData?.map((item) => ({
    type: `Status ${item.status}`,
    value: item.count,
  }));

  const methodChartData = methodData?.map((item) => ({
    type: item.method,
    value: item.count,
  }));

  const durationChartData = durationData?.map((item) => ({
    path: item.path.split("/").pop() || item.path,
    duration: item.duration,
  }));

  const timeSeriesChartData = timeSeriesData?.map((item) => ({
    date: item.date,
    count: item.count,
  }));

  // Chart configurations
  const pieConfig = {
    appendPadding: 10,
    data: statusCodeChartData,
    angleField: "value",
    colorField: "type",
    radius: 0.8,
    label: {
      text: "value",
      position: "outside",
      style: {
        fontWeight: "bold",
      },
    },
    // interactions: [
    //   {
    //     type: "element-active",
    //   },
    // ],
  };

  const methodPieConfig = {
    appendPadding: 10,
    data: methodChartData,
    radius: 0.8,
    angleField: "value",
    colorField: "type",
    label: {
      text: "value",
      position: "outside",
      style: {
        fontWeight: "bold",
      },
    },
  };

  const columnConfig = {
    data: durationChartData,
    xField: "path",
    yField: "duration",
    label: {
      //   position: "middle",
      style: {
        fill: "#FFFFFF",
      },
    },
    xAxis: {
      label: {
        autoRotate: true,
        autoHide: false,
      },
    },
  };

  const lineConfig = {
    data: timeSeriesChartData,
    xField: "date",
    yField: "count",
    point: {
      size: 5,
      shape: "diamond",
    },
    label: {
      style: {
        fill: "#aaa",
      },
    },
  };

  return (
    <div style={{ padding: "20px" }}>
      <Row
        justify="space-between"
        align="middle"
        style={{ marginBottom: "20px" }}
      >
        <Title level={3}>API Logs Analytics</Title>
        <Space>
          <RangePicker
            value={dateRange}
            onChange={handleDateRangeChange}
            style={{ marginRight: "10px" }}
          />
          <Button
            type="primary"
            icon={<ReloadOutlined />}
            onClick={handleRefresh}
          ></Button>
        </Space>
      </Row>

      <Row gutter={[16, 16]}>
        <Col span={6}>
          <Card>
            <Statistic
              title="Total Requests"
              value={stats.totalRequests}
              valueStyle={{ color: "#3f8600" }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Avg Duration (ms)"
              value={stats.avgDuration.toFixed(2)}
              valueStyle={{ color: "#cf1322" }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Success Rate"
              value={stats.successRate.toFixed(2)}
              suffix="%"
              valueStyle={{ color: "#3f8600" }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Error Rate"
              value={stats.errorRate.toFixed(2)}
              suffix="%"
              valueStyle={{ color: "#cf1322" }}
            />
          </Card>
        </Col>
      </Row>

      <Row gutter={[16, 16]} style={{ marginTop: "20px" }}>
        <Col span={12}>
          <Card title="Status Code Distribution">
            <Pie {...pieConfig} />
          </Card>
        </Col>
        <Col span={12}>
          <Card title="HTTP Method Distribution">
            <Pie {...methodPieConfig} />
          </Card>
        </Col>
      </Row>

      <Row gutter={[16, 16]} style={{ marginTop: "20px" }}>
        <Col span={24}>
          <Card title="Top 10 Slowest Endpoints">
            <Column {...columnConfig} />
          </Card>
        </Col>
      </Row>

      <Row gutter={[16, 16]} style={{ marginTop: "20px" }}>
        <Col span={24}>
          <Card title="Request Volume Over Time">
            <Line {...lineConfig} />
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default AnalyticsChart;

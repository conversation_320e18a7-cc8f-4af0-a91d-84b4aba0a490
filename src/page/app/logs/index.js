import { Breadcrumb, Row, Tabs } from "antd";
import React, { useState } from "react";
import CRUDComponent from "../../../component/common/CRUD-Component";
import CONSTANTS from "../../../util/constant/CONSTANTS";
import { ROLES } from "../../../util/constant/dataConstants";
import AnalyticsChart from "./AnalyticsChart";

const LogsPage = (props) => {
  const [activeTab, setActiveTab] = useState("1");

  const items = [
    {
      key: "1",
      label: "Logs Table",
      children: (
        <CRUDComponent
          GET={{
            API: CONSTANTS.API.Logs.getAll,
            DataModifier: (res, API, Setrefresh) => {
              return res?.map((el) => ({
                ...el,
              }));
            },
            column: CONSTANTS.TABLE.LOGS,
          }}
          DELETE={{
            API: CONSTANTS.API.Logs.delete,
            message: "Deleted Successfully",
            confirm_message: "Are you sure to Delete this Logs?",
          }}
          datefilter
          isSearch
          selectionOff
          props={props}
        />
      ),
    },
    {
      key: "2",
      label: "Analytics Dashboard",
      children: <AnalyticsChart />,
    },
  ];

  return (
    <>
      <Breadcrumb
        className="mx-2 mb-3 mt-6"
        separator=" > "
        items={[
          {
            title: "Home",
          },
          {
            title: "Logs",
          },
        ]}
      />
      <Row>
        <Tabs
          activeKey={activeTab}
          items={items}
          onChange={setActiveTab}
          style={{ width: "100%" }}
        />
      </Row>
    </>
  );
};

export default LogsPage;

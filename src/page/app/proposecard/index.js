import { Breadcrumb, Row } from "antd";
import React from "react";
import CRUDComponent from "../../../component/common/CRUD-Component";
import CONSTANTS from "../../../util/constant/CONSTANTS";

const ProposecardPage = (props) => {
  // const API = useHttp();
  // useEffect(() => {
  //   API.sendRequest(
  //     CONSTANTS.API.Language.getAll,
  //     (res) => {
  //       const LangugesArr = res?.data?.rows?.map((el) => ({
  //         ...el,
  //         label: ` ${el?.name} ( ${el?.code} - ${el?.englishName})`,
  //         // label: `${el?.code} - ${el?.name} ( ${el?.englishName})`,
  //         value: el?.id,
  //       }));
  //       CONSTANTS.FORM_FIELD.PROPOSECARD.find(
  //         (el) => el?.id === "languageId"
  //       ).option = LangugesArr;
  //       // CONSTANTS.FORM_FIELD.GROUP.find(
  //       //   (el) => el?.id === "languageId"
  //       // ).option = LangugesArr;
  //       // CONSTANTS.FORM_FIELD.COURSE.find(
  //       //   (el) => el?.id === "languageId"
  //       // ).option = LangugesArr;
  //     }
  //     // { isActive: true, isPremium: true }
  //   );
  // }, []);
  return (
    <>
      <Breadcrumb
        className="mx-2 mb-3 mt-6"
        separator=" > "
        items={[
          {
            title: "Home",
          },
          {
            // href: `${appRoot}/${ROUTES.Proposecard}`,
            title: "Proposecard",
          },
        ]}
      />
      <Row>
        <CRUDComponent
          GET={{
            API: CONSTANTS.API.Proposecard.getAll,
            DataModifier: (res, API, Setrefresh) => {
              return res?.map((el) => ({
                ...el,
                // no: `${el?.id}`.padStart(6, "0"),
                // status: {
                //   checked: el?.isActive,
                //   id: el.id,
                //   onClick: (id, val) => {
                //     const UpdateAPIEnd = { ...CONSTANTS?.API.Proposecard.update };
                //     UpdateAPIEnd.endpoint = `${UpdateAPIEnd?.endpoint}${id}`;
                //     API.sendRequest(
                //       UpdateAPIEnd,
                //       () => {
                //         Setrefresh((prev) => !prev);
                //       },
                //       { isActive: !val },
                //       `Proposecard ${
                //         !val ? "activated" : "deactivated"
                //       } Successfully`,
                //       () => {},
                //       "Currently Not working We will update Soon"
                //     );
                //   },
                // },
              }));
            },
            column: CONSTANTS.TABLE.PROPOSECARD,
          }}
          // UPDATE={{
          //   API: CONSTANTS.API.Proposecard.update,
          //   message: "Updated Proposecard successfully",
          //   modaltitle: "Update Proposecard",
          //   modalFields: CONSTANTS.FORM_FIELD.PROPOSECARD,
          //   // payloadModifier: (res) => res,
          // }}
          // CREATE={
          //   [
          //     ROLES.ADMIN,
          //     ROLES.DATA,
          //     // ROLES.IMAGE,
          //     // ROLES.INTERN,
          //     // ROLES.SEO,
          //     ROLES.SUPER_ADMIN,
          //     // ROLES.SUPERUSER,
          //     // ROLES.WRITER,
          //   ].some((el) => el === props?.profile?.role) && {
          //     name: "Add a Proposecard",
          //     API: CONSTANTS.API.Proposecard.create,
          //     message: "Created Proposecard successfully",
          //     modaltitle: "Add Proposecard",
          //     modalFields: CONSTANTS.FORM_FIELD.PROPOSECARD,
          //   }
          // }
          DELETE={{
            API: CONSTANTS.API.Proposecard.delete,
            message: "Deleted Successfully",
            confirm_message: "Are you sure to Delete this category?",
          }}
          // FILTERSORTKEY={{
          //   cityName: "$city.name$",
          // }}
          datefilter
          isSearch
          selectionOff
          props={props}
        />
      </Row>
    </>
  );
};

export default ProposecardPage;

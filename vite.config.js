import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  esbuild: {
    loader: "jsx",
    include: /src\/.*\.[jt]sx?$/,
    exclude: [],
  },
  server: {
    port: 3000,
    open: true,
    host: true,
  },
  build: {
    outDir: "build",
    sourcemap: true,
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ["react", "react-dom"],
          antd: ["antd"],
          charts: ["@ant-design/charts"],
          mapbox: ["mapbox-gl", "react-mapbox-gl"],
        },
      },
    },
  },
  define: {
    global: "globalThis",
  },
  optimizeDeps: {
    include: [
      "react",
      "react-dom",
      "antd",
      "dayjs",
      "axios",
      "react-router-dom",
      "styled-components",
    ],
  },
  css: {
    postcss: "./postcss.config.js",
  },
});
